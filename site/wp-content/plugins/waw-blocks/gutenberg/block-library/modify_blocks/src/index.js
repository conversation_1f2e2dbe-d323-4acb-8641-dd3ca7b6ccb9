import { registerBlockStyle, registerBlockVariation, registerBlockType } from '@wordpress/blocks';
import { quote as icon } from '@wordpress/icons';
import { withSelect } from '@wordpress/data';
import { InspectorControls } from '@wordpress/block-editor';
import { PanelBody, SelectControl } from '@wordpress/components';
import { addFilter } from '@wordpress/hooks';
import { createHigherOrderComponent } from '@wordpress/compose';
import { __ } from '@wordpress/i18n';

import './modules/paragraph_block';
import './modules/submenu';
import './modules/row_block';
import './modules/title_block';
import './modules/table';
// import './modules/responsive';
import './modules/cover_block';
import './modules/gallery_block';
import './modules/buttons';
import './modules/button_icon';
import './modules/media_text';
import './modules/query';
import './modules/image_block';
// import './modules/submenu_allow_block';

var modify_blocks = new (function () {
	wp.domReady(function () {
		//COLUMNS BLOCK
		registerBlockStyle('core/columns', [
			{
				name: 'no-gaps',
				label: 'Columns no gaps',
			},
			{
				name: '20-gap',
				label: '20px Gap',
			},
			{
				name: '50-gap',
				label: '50px Gap',
			},
			{
				name: '80-gap',
				label: '80px Gap',
			},
			{
				name: '135-gap',
				label: '135px Gap',
			},
		]);
		registerBlockStyle('core/query', [
			{
				name: 'two-columns',
				label: 'Two Columns',
			},
			{
				name: 'three-columns',
				label: 'Three Columns',
			},
			{
				name: 'list-view',
				label: 'List View',
			},
		]);
		// unregisterBlockStyle("core/button", ["default", "outline", "squared", "fill"]);

		// <CustomPostType>
		// ----------------
		const MY_VARIATION_NAME = 'waw-blocks/course-list';

		registerBlockVariation('core/query', {
			name: MY_VARIATION_NAME,
			title: 'WAW Courses List',
			description: 'Displays a list of courses',
			isActive: ({ namespace, query }) => {
				return namespace === MY_VARIATION_NAME && query.postType === 'course';
			},
			icon: icon,
			attributes: {
				namespace: MY_VARIATION_NAME,
				query: {
					perPage: 6,
					pages: 0,
					offset: 0,
					postType: 'course',
					order: 'desc',
					orderBy: 'date',
					author: '',
					search: '',
					exclude: [],
					sticky: '',
					inherit: false,
				},
			},
			scope: ['inserter'],
			innerBlocks: [['core/post-template', {}, [['core/post-title'], ['core/post-excerpt']]], ['core/query-pagination'], ['core/query-no-results']],
		});

		/**
		 * --------------------
		 * Navigation (Mega Menu)
		 * --------------------
		 *
		 * @param {*} settings
		 * @param {*} name
		 * @returns
		 */

		// const addReusableBlockAttribute = (settings, name) => {
		// 	if (name !== 'core/navigation-link') {
		// 		return settings;
		// 	}

		// 	return {
		// 		...settings,
		// 		attributes: {
		// 			...settings.attributes,
		// 			reusableBlockId: {
		// 				type: 'number',
		// 				default: 0,
		// 			},
		// 		},
		// 	};
		// };

		// addFilter('blocks.registerBlockType', 'waw-blocks/add-reusable-block-attribute', addReusableBlockAttribute);

		// const withCustomInspectorControl = createHigherOrderComponent((BlockEdit) => {
		// 	return (props) => {
		// 		// Only extend for navigation link blocks
		// 		if (props.name !== 'core/navigation-link') {
		// 			return <BlockEdit {...props} />;
		// 		}
		// 		var options = [{ value: 0, label: __('Select a Reusable Block', 'textdomain') }];

		// 		if (reusableBlocks) {
		// 			reusableBlocks.forEach(function (block) {
		// 				options.push({ value: block.id, label: block.title.rendered });
		// 			});
		// 		}

		// 		const { attributes, setAttributes } = props;
		// 		const { reusableBlockId } = attributes;

		// 		const onChangeReusableBlockId = (value) => {
		// 			setAttributes({ reusableBlockId: value });
		// 		};

		// 		return (
		// 			<>
		// 				<BlockEdit {...props} />
		// 				<InspectorControls>
		// 					<PanelBody title={__('My Custom Dropdown', 'textdomain')}>
		// 						<SelectControl
		// 							label={__('Select Reusable Block', 'textdomain')}
		// 							value={reusableBlockId}
		// 							options={options}
		// 							onChange={onChangeReusableBlockId}
		// 						/>
		// 					</PanelBody>
		// 				</InspectorControls>
		// 			</>
		// 		);
		// 	};
		// }, 'withCustomInspectorControl');

		// addFilter('editor.BlockEdit', 'my-plugin/with-custom-inspector-control', withCustomInspectorControl);

		// Create a higher-order component to add the SelectControl
		// const withReusableBlockControl = createHigherOrderComponent((BlockEdit) => {
		// 	return withSelect((select, ownProps) => {
		// 		const { getBlockParents } = select('core/block-editor');
		// 		const parents = getBlockParents(ownProps.clientId);
		// 		const parentBlock = parents.length ? select('core/block-editor').getBlock(parents[0]) : null;
		// 		const reusableBlocks = select('core').getEntityRecords('postType', 'wp_block', { per_page: -1 });

		// 		return {
		// 			parentBlock,
		// 			reusableBlocks,
		// 		};
		// 	})((props) => {
		// 		// Check if we're editing a navigation-link block
		// 		if (props.name !== 'core/navigation-link') {
		// 			return <BlockEdit {...props} />;
		// 		}

		// 		const { parentBlock, reusableBlocks, attributes, setAttributes } = props;
		// 		const { reusableBlockId } = attributes;

		// 		// Only show the control for submenu items
		// 		console.log(parentBlock);
		// 		// if (!parentBlock || parentBlock.name !== 'core/navigation-link') {
		// 		// 	return <BlockEdit {...props} />;
		// 		// }

		// 		const options = [{ value: 0, label: __('Select a Reusable Block', 'textdomain') }];

		// 		if (reusableBlocks) {
		// 			reusableBlocks.forEach((block) => {
		// 				options.push({ value: block.id, label: block.title.raw });
		// 			});
		// 		}

		// 		const onChangeReusableBlockId = (newValue) => {
		// 			setAttributes({ reusableBlockId: parseInt(newValue, 10) });
		// 		};

		// 		return (
		// 			<div>
		// 				<BlockEdit {...props} />
		// 				<SelectControl
		// 					label={__('Select Reusable Block', 'textdomain')}
		// 					value={reusableBlockId}
		// 					options={options}
		// 					onChange={onChangeReusableBlockId}
		// 				/>
		// 			</div>
		// 		);
		// 	});
		// }, 'withReusableBlockControl');

		// wp.hooks.addFilter('editor.BlockEdit', 'waw-blocks/with-reusable-block-control', withReusableBlockControl);

		/**
		 * ALLOW CUSTOM BLOCKS
		 * @ CORE/NAVIGATION
		 */

		// const allowAdditionalBlocksInNavigation = (settings, name) => {
		// 	if (name !== 'core/navigation') {
		// 		return settings;
		// 	}

		// 	const allowedBlocks = settings.allowedBlocks || [];

		// 	// Add your custom blocks to the list
		// 	// For example, adding 'my-custom/block'
		// 	allowedBlocks.push('waw-blocks/submenu');

		// 	return {
		// 		...settings,
		// 		allowedBlocks,
		// 	};
		// };

		// addFilter('blocks.registerBlockType', 'waw-blocks/allow-additional-blocks-in-navigation', allowAdditionalBlocksInNavigation);
		// const withCustomQueryControls = (BlockEdit) => {
		// 	return (props) => {
		// 		const isMyCustomQueryVariation = props.name === 'core/query' && props.attributes.query.postType === "course";
		// 		if(isMyCustomQueryVariation) {
		// 			console.log("PROPS", props);
		// 		}
		// 		const updatePerPage = (value) => {
		// 			const newQuery = { ...props.attributes.query, perPage: value };
		// 			props.setAttributes({ query: newQuery });
		// 		};

		// 		return (
		// 			<>
		// 				<BlockEdit {...props} />
		// 				{isMyCustomQueryVariation && (
		// 					<InspectorControls>
		// 						<PanelBody title="Course List Settings">
		// 							<RangeControl
		// 								label="Number of Courses per Page"
		// 								value={props.attributes.query.perPage}
		// 								onChange={updatePerPage}
		// 								min={1}
		// 								max={20}
		// 							/>
		// 							{/* Add more controls as needed */}
		// 						</PanelBody>
		// 					</InspectorControls>
		// 				)}
		// 			</>
		// 		);
		// 	};
		// };

		// addFilter("editor.BlockEdit", "waw-blocks/course-list", withCustomQueryControls);
		// ----------------
		//</CustomPostType>

		// Add selector
		// Reusable blocks to be available
		// In the submenu
		// registerBlockType('core/navigation-link', {
		// 	attributes: {
		// 		reusableBlockId: {
		// 			type: 'number',
		// 			default: 0,
		// 		},
		// 	},

		// 	edit: withSelect(function (select, props) {
		// 		console.log('Test Navigation Link');
		// 		return {
		// 			reusableBlocks: select('core').getEntityRecords('postType', 'wp_block', { per_page: -1 }),
		// 		};
		// 	})(function (props) {
		// 		var reusableBlockId = props.attributes;
		// 		var reusableBlocks = props.reusableBlocks;
		// 		var options = [{ value: 0, label: __('Select a Reusable Block', 'textdomain') }];

		// 		if (reusableBlocks) {
		// 			reusableBlocks.forEach(function (block) {
		// 				options.push({ value: block.id, label: block.title.rendered });
		// 			});
		// 		}

		// 		function onChangeReusableBlockId(newValue) {
		// 			props.setAttributes({ reusableBlockId: parseInt(newValue, 10) });
		// 		}

		// 		return (
		// 			<div className={props.className}>
		// 				{/* ... existing navigation link UI */}
		// 				<SelectControl
		// 					label={__('Select Reusable Block', 'textdomain')}
		// 					value={reusableBlockId}
		// 					options={options}
		// 					onChange={onChangeReusableBlockId}
		// 				/>
		// 				{/* ... other components */}
		// 			</div>
		// 		);
		// 	}),

		// 	save: function (props) {
		// 		// ... Existing save function
		// 		// Save the reusableBlockId attribute
		// 	},
		// });
	}); // </wp.domReady>
})();
