/**
 * <PERSON>ton Icon Styles
 * Styles for icons added to core/button blocks
 */

.wp-block-button {
	.wp-button-icon {
		display: inline-block;
		vertical-align: middle;
		flex-shrink: 0;
		
		// Ensure icon doesn't break button layout
		max-width: none;
		height: auto;
		
		// Handle SVG icons
		&.svg-icon {
			fill: currentColor;
			stroke: currentColor;
		}
		
		// Responsive adjustments
		@media (max-width: 768px) {
			// Slightly smaller icons on mobile
			transform: scale(0.9);
		}
	}
	
	// Ensure proper alignment when icon is present
	&.has-icon {
		.wp-block-button__link {
			display: inline-flex;
			align-items: center;
			gap: 0; // Gap is handled by margin on icon
		}
	}
	
	// Icon position specific styles
	&.icon-left {
		.wp-button-icon {
			order: -1;
		}
	}
	
	&.icon-right {
		.wp-button-icon {
			order: 1;
		}
	}
}

// Editor specific styles
.block-editor-block-list__layout {
	.wp-block-button {
		.wp-button-icon {
			pointer-events: none; // Prevent icon from interfering with button editing
		}
	}
}

// Ensure icons work well with different button styles
.wp-block-button {
	// Outline style
	&.is-style-outline {
		.wp-button-icon {
			// Icons should inherit the text color in outline buttons
			color: inherit;
		}
	}
	
	// Fill style (default)
	&.is-style-fill,
	&:not([class*="is-style-"]) {
		.wp-button-icon {
			// Icons should work with button background
			color: inherit;
		}
	}
}

// High contrast mode support
@media (prefers-contrast: high) {
	.wp-block-button .wp-button-icon {
		filter: contrast(1.2);
	}
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
	.wp-block-button .wp-button-icon {
		transition: none;
	}
}
