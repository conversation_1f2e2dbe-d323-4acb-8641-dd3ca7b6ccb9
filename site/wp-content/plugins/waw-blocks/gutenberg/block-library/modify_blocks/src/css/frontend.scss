@import 'button-icon';

.wp-block-columns {
	&.is-style-20-gap {
		gap: 20px !important;
		margin-left: 0;
		margin-right: 0;
	}
	&.is-style-50-gap {
		gap: 50px !important;
		margin-left: 0;
		margin-right: 0;
	}
	&.is-style-135-gap {
		gap: 50px;

		@media (min-width: 1024px) {
			gap: 135px !important;
		}
	}
	&.is-style-80-gap {
		gap: 80px !important;

		@media (max-width: 1280px) {
			gap: 40px !important;
		}
	}
	&.is-style-no-gaps {
		gap: 0;
	}
}

.wp-block-group {
	&.is-show-seperator-y {
		border-bottom: 1px solid #dddddd;
	}
	&.is-show-seperator {
		gap: 60px;

		> *:not(:last-child) {
			position: relative;

			&::after {
				content: '';
				position: absolute;
				right: -30px;
				height: 100%;
				width: 1px;
				background-color: white;

				// we should use a util class to content none,
				// in some cases we dont want to hide the seperator on mobile
				// @media only screen and (max-width: 1200px) {
				// 	content: none;
				// }
			}
		}
	}
	&.is-stacked {
		/* CSS rules for smaller screens go here */

		@media only screen and (max-width: 768px) {
			display: flex;
			flex-direction: column;
			gap: 20px;
		}
	}
}

.wp-block-separator {
	border: none;
	border-top: 1px solid;
}

.wp-swiper.is-style-thumbnails-bottom-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:first-child {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}

.wp-swiper.is-style-thumbnails-bottom-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child {
	position: absolute;
	background: rgba(255, 255, 255, 0.8);
	z-index: 1;
	max-width: 40%;
	right: auto;
	left: 5%;
	padding: 40px;
	border-radius: 20px;
	top: 50%;
	transform: translateY(-50%);
	max-width: 614px;
}

body .wp-swiper.is-style-thumbnails-bottom-right .wp-swiper__thumbs {
	position: absolute;
	right: 10%;
	width: 40%;
	transform: translateY(-50%);
	z-index: 1;
}

.wp-block-table {
	thead {
		background-color: #1f1f1f;
		color: #fff;
		height: 47px;
		border-top-color: #dddddd;
		border-left-color: #000;
		border-bottom: none;

		th {
			border-left-color: #000;
			border-right-color: #000;

			font-weight: 400;
			strong {
				font-weight: 700;
			}
		}
	}
	tbody tr {
		> td {
			border-right: 1px solid #ddd;
		}

		td {
			border-top-color: #dddddd;
			border-bottom-color: #dddddd;
			border-left-color: #dddddd;
			padding: 20px 40px;
		}
	}
	tbody tr:nth-child(odd) {
		background-color: #f9f9f9;
	}

	&.is-style-plain {
	}

	&.is-style-stripes {
		tbody tr {
			background-color: #eee;

			> td {
				border-right: 1px solid #ddd;
			}

			td {
				padding: 20px 40px;
			}
		}
		tbody tr:nth-child(odd) {
			background-color: #f9f9f9;
		}
	}
}

.wp-block-query {
	ul.wp-block-post-template {
		gap: 50px;

		> li {
			display: flex;
			flex-direction: column;

			figure {
				overflow: hidden;
				cursor: pointer;
				img {
					transition: all 0.3s ease-in-out;
				}
			}

			&:hover {
				img {
					transition: all 0.3s ease-in-out;
					transform: scale(1.1);
				}
			}
		}
	}

	// Loop to generate gap classes
	@for $i from 0 through 10 {
		$value: $i * 10;

		// Row gap classes
		&.has-row-gap-#{$value} {
			ul.wp-block-post-template {
				row-gap: #{$value}px; // Adjust to suit your design
			}
		}

		// Column gap classes
		&.has-column-gap-#{$value} {
			ul.wp-block-post-template {
				column-gap: #{$value}px; // Adjust to suit your design
			}
		}
	}

	.wp-block-post-featured-image {
		height: 246px;
		img {
			object-fit: cover;
			height: 100%;
			width: 100%;
		}
	}
	&.is-style-two-columns {
		ul.wp-block-post-template {
			display: grid;
			grid-auto-rows: minmax(min-content, max-content); /* Adjusts row height based on content */

			li {
				height: auto;
			}

			@media (min-width: 1281px) {
				grid-template-columns: repeat(2, 1fr);
			}
		}
	}
	&.is-style-three-columns {
		ul.wp-block-post-template {
			display: grid;

			grid-auto-rows: minmax(min-content, max-content); /* Adjusts row height based on content */

			li {
				height: auto;
			}

			.wp-block-buttons.is-content-justification-right {
				align-items: center;
			}
			.wp-block-buttons.is-layout-flex.wp-block-buttons-is-layout-flex {
				justify-content: center;
			}

			@media (min-width: 1400px) {
				grid-template-columns: repeat(3, 1fr);

				li {
					.wp-block-columns {
						flex-wrap: wrap !important;
						flex-direction: column;
					}
				}
			}
		}
	}
	&.is-style-list-view {
		ul.wp-block-post-template {
			padding: 0 50px;

			li {
				margin-bottom: 0;

				.wp-block-post-title {
					margin: 0;
				}
				a {
					font-size: 16px;
					font-weight: 500;
					line-height: 34px;
					letter-spacing: 0em;
					text-align: center;
					color: #111;
				}
			}
		}
		.wp-block-query-pagination {
			justify-content: center;

			.wp-block-query-pagination-numbers {
				.page-numbers {
					font-size: 16px;
					font-weight: 300;
					line-height: 28px;
					letter-spacing: 0px;
					text-align: left;
					padding: 0 5px;
					color: rgba(0, 0, 0, 0.4);
				}
				.current {
					color: var(--wp--preset--color--primary);
				}
			}
		}
	}
}

.wp-block-table {
	&.is-style-plain {
		tbody {
			tr {
				background: none;

				td {
					&:first-child {
						padding-right: 10px;
					}
				}
			}
			td {
				padding: 10px 0;
				border: none;
				vertical-align: baseline;
			}
		}
	}
}

.wp-block-cover {
	&.is-stretch-to-fill {
		height: 100%;
	}
}

.wp-block-gallery {
	&.columns-default {
		&.is-style-stack {
			flex-direction: column;

			figure {
				figcaption {
					position: relative;
					background: none;
					color: #000;
					text-align: right;
					padding-left: 0;
					padding-right: 0;
				}
			}

			figure:last-of-type {
				margin: -20% 0 0 -20% !important;

				img {
					max-width: 50%;
				}
			}
		}
	}
}

p {
	&.is-style-pill {
		display: inline-block;
		border-radius: 12px;
		height: 36px;
		line-height: 36px;
		padding: 0 20px;
		text-transform: uppercase;
	}
}

// Fix image block shadow and border radius - move from img to figure when padding is present
.wp-block-image[style*='padding'] {
	// Apply actual shadow values (not CSS custom properties to avoid malformed values)
	box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15) !important;
	border-radius: 10px !important;

	// More aggressive removal from img
	img[style] {
		box-shadow: none !important;
		border-radius: 0 !important;
	}

	// Target specific inline styles
	img[style*='box-shadow'] {
		box-shadow: none !important;
	}

	img[style*='border-radius'] {
		border-radius: 0 !important;
	}
}
