import { addFilter } from '@wordpress/hooks';
import { createHigherOrderComponent } from '@wordpress/compose';
import { Fragment } from '@wordpress/element';
import { InspectorControls, MediaUpload, MediaUploadCheck } from '@wordpress/block-editor';
import { Panel, PanelBody, PanelRow, SelectControl, Button, BaseControl } from '@wordpress/components';
import { more } from '@wordpress/icons';
import { __ } from '@wordpress/i18n';

// Add icon attributes to the core/button block
const addIconAttributes = (settings, name) => {
	if (name !== 'core/button') {
		return settings;
	}

	settings.attributes = {
		...settings.attributes,
		iconId: {
			type: 'number',
			default: 0,
		},
		iconUrl: {
			type: 'string',
			default: '',
		},
		iconPosition: {
			type: 'string',
			default: 'left',
		},
		iconSize: {
			type: 'string',
			default: '16px',
		},
		iconSpacing: {
			type: 'string',
			default: '8px',
		},
	};

	return settings;
};

// Add icon controls to the button block inspector
const withIconControls = createHigherOrderComponent((BlockEdit) => {
	return (props) => {
		if (props.name !== 'core/button') {
			return <BlockEdit {...props} />;
		}

		const { attributes, setAttributes } = props;
		const { iconId, iconUrl, iconPosition, iconSize, iconSpacing } = attributes;

		const onSelectIcon = (media) => {
			setAttributes({
				iconId: media.id,
				iconUrl: media.url,
			});
		};

		const removeIcon = () => {
			setAttributes({
				iconId: 0,
				iconUrl: '',
			});
		};

		return (
			<Fragment>
				<BlockEdit {...props} />
				<InspectorControls>
					<Panel>
						<PanelBody
							title="Button Icon"
							icon={more}
							initialOpen={false}
						>
							<PanelRow>
								<BaseControl label="Icon">
									<MediaUploadCheck>
										<MediaUpload
											onSelect={onSelectIcon}
											allowedTypes={['image']}
											value={iconId}
											render={({ open }) => (
												<div>
													{iconUrl ? (
														<div style={{ marginBottom: '10px' }}>
															<img 
																src={iconUrl} 
																alt="Selected icon" 
																style={{ 
																	maxWidth: '50px', 
																	maxHeight: '50px',
																	display: 'block',
																	marginBottom: '8px'
																}} 
															/>
															<Button 
																onClick={open}
																variant="secondary"
																style={{ marginRight: '8px' }}
															>
																{__('Change Icon')}
															</Button>
															<Button 
																onClick={removeIcon}
																variant="secondary"
																isDestructive
															>
																{__('Remove Icon')}
															</Button>
														</div>
													) : (
														<Button 
															onClick={open}
															variant="secondary"
														>
															{__('Select Icon')}
														</Button>
													)}
												</div>
											)}
										/>
									</MediaUploadCheck>
								</BaseControl>
							</PanelRow>

							{iconUrl && (
								<>
									<PanelRow>
										<SelectControl
											label="Icon Position"
											value={iconPosition}
											options={[
												{ label: 'Left', value: 'left' },
												{ label: 'Right', value: 'right' },
											]}
											onChange={(value) => setAttributes({ iconPosition: value })}
										/>
									</PanelRow>

									<PanelRow>
										<SelectControl
											label="Icon Size"
											value={iconSize}
											options={[
												{ label: '12px', value: '12px' },
												{ label: '14px', value: '14px' },
												{ label: '16px', value: '16px' },
												{ label: '18px', value: '18px' },
												{ label: '20px', value: '20px' },
												{ label: '24px', value: '24px' },
												{ label: '32px', value: '32px' },
											]}
											onChange={(value) => setAttributes({ iconSize: value })}
										/>
									</PanelRow>

									<PanelRow>
										<SelectControl
											label="Icon Spacing"
											value={iconSpacing}
											options={[
												{ label: '4px', value: '4px' },
												{ label: '6px', value: '6px' },
												{ label: '8px', value: '8px' },
												{ label: '10px', value: '10px' },
												{ label: '12px', value: '12px' },
												{ label: '16px', value: '16px' },
											]}
											onChange={(value) => setAttributes({ iconSpacing: value })}
										/>
									</PanelRow>
								</>
							)}
						</PanelBody>
					</Panel>
				</InspectorControls>
			</Fragment>
		);
	};
}, 'withIconControls');

// Add icon to the button element on save
const addIconToButtonSave = (element, blockType, attributes) => {
	if (blockType.name !== 'core/button') {
		return element;
	}

	const { iconUrl, iconPosition, iconSize, iconSpacing } = attributes;

	if (!iconUrl) {
		return element;
	}

	// Create icon element
	const iconElement = {
		type: 'img',
		props: {
			src: iconUrl,
			alt: '',
			className: 'wp-button-icon',
			style: {
				width: iconSize,
				height: iconSize,
				display: 'inline-block',
				verticalAlign: 'middle',
				...(iconPosition === 'left' ? { marginRight: iconSpacing } : { marginLeft: iconSpacing }),
			},
		},
	};

	// Clone the element and modify its children
	const newElement = { ...element };
	
	if (newElement.props && newElement.props.children) {
		const children = Array.isArray(newElement.props.children) 
			? [...newElement.props.children] 
			: [newElement.props.children];

		// Add icon based on position
		if (iconPosition === 'left') {
			children.unshift(iconElement);
		} else {
			children.push(iconElement);
		}

		newElement.props = {
			...newElement.props,
			children: children,
		};
	}

	return newElement;
};

// Register the filters
addFilter('blocks.registerBlockType', 'waw-blocks/add-button-icon-attributes', addIconAttributes);
addFilter('editor.BlockEdit', 'waw-blocks/add-button-icon-controls', withIconControls);
addFilter('blocks.getSaveElement', 'waw-blocks/add-button-icon-save', addIconToButtonSave);
