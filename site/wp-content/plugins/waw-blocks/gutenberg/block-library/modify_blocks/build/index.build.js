(()=>{var e={20:(e,t,o)=>{"use strict";var l=o(609),r=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,o){var l,c={},s=null,p=null;for(l in void 0!==o&&(s=""+o),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(p=t.ref),t)n.call(t,l)&&!i.hasOwnProperty(l)&&(c[l]=t[l]);if(e&&e.defaultProps)for(l in t=e.defaultProps)void 0===c[l]&&(c[l]=t[l]);return{$$typeof:r,type:e,key:s,ref:p,props:c,_owner:a.current}}},38:()=>{wp.hooks.addFilter("blocks.registerBlockType","custom/media-text-modify-save",(e,t)=>e)},609:e=>{"use strict";e.exports=window.React},848:(e,t,o)=>{"use strict";e.exports=o(20)}},t={};function o(l){var r=t[l];if(void 0!==r)return r.exports;var n=t[l]={exports:{}};return e[l](n,n.exports,o),n.exports}(()=>{"use strict";const e=window.wp.blocks,t=window.wp.primitives;var l=o(848);const r=(0,l.jsx)(t.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)(t.Path,{d:"M13 6v6h5.2v4c0 .8-.2 1.4-.5 1.7-.6.6-1.6.6-2.5.5h-.3v1.5h.5c1 0 2.3-.1 3.3-1 .6-.6 1-1.6 1-2.8V6H13zm-9 6h5.2v4c0 .8-.2 1.4-.5 1.7-.6.6-1.6.6-2.5.5h-.3v1.5h.5c1 0 2.3-.1 3.3-1 .6-.6 1-1.6 1-2.8V6H4v6z"})}),n=window.wp.data,a=window.wp.blockEditor,i=window.wp.components,c=window.wp.hooks,s=window.wp.compose,p=window.wp.i18n;var u=o(609);const d=window.wp.richText;(0,d.registerFormatType)("core/underline",{title:"Underline",tagName:"u",className:null,edit:({isActive:e,onChange:t,value:o})=>((0,n.useSelect)(e=>e("core/block-editor").getSelectedBlock(),[]),(0,u.createElement)(a.RichTextToolbarButton,{icon:"editor-underline",title:"Underline",onClick:()=>{console.log("asdfas",o),t((0,d.toggleFormat)(o,{type:"core/underline"}))},isActive:e}))}),(0,e.registerBlockStyle)("core/paragraph",[{name:"pill",label:"Pill"}]),new function(){wp.domReady(function(){(0,c.addFilter)("blocks.registerBlockType","waw-blocks/add-reusable-block-attribute",(e,t)=>"core/navigation-link"!==t?e:{...e,attributes:{...e.attributes,reusableBlockId:{type:"number",default:0}}});const e=(0,s.createHigherOrderComponent)(e=>(0,n.withSelect)((e,t)=>{if("core/navigation-link"!==t.name)return{};const{getBlockParents:o}=e("core/block-editor"),l=o(t.clientId).some(t=>{const o=e("core/block-editor").getBlock(t);return o&&"core/navigation-submenu"===o.name});return{hasSubmenuParent:l,reusableBlocks:l?e("core").getEntityRecords("postType","wp_block",{per_page:-1}):[]}})(t=>{if(!t.hasSubmenuParent)return(0,u.createElement)(e,{...t});const{attributes:o,setAttributes:l,reusableBlocks:r}=t,{reusableBlockId:n}=o,c=[{value:0,label:(0,p.__)("Select a Reusable Block","textdomain")}];return r&&r.forEach(e=>{c.push({value:e.id,label:e.title.raw})}),(0,u.createElement)(u.Fragment,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(i.PanelBody,{title:(0,p.__)("Reusable Block for Submenu","textdomain")},(0,u.createElement)(i.SelectControl,{label:(0,p.__)("Select Reusable Block","textdomain"),value:n,options:c,onChange:e=>{l({reusableBlockId:parseInt(e,10)})}}))))}),"withReusableBlockControl");(0,c.addFilter)("editor.BlockEdit","waw-blocks/with-reusable-block-control",e)})};const m=window.wp.element,b=(0,l.jsx)(t.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)(t.Path,{d:"M4 9v1.5h16V9H4zm12 5.5h4V13h-4v1.5zm-6 0h4V13h-4v1.5zm-6 0h4V13H4v1.5z"})}),g=(0,s.createHigherOrderComponent)(e=>t=>"core/group"!==t.name?(0,u.createElement)(e,{...t}):(0,u.createElement)(m.Fragment,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(i.Panel,null,(0,u.createElement)(i.PanelBody,{title:"Extra Settings",icon:b,initialOpen:!0},(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.ToggleControl,{label:"Show Seperator X",help:"Adds a border to the right of the item. Great for objects arranged in a row. Example: Item | Item | Item",checked:t.attributes.hasSeparator,onChange:e=>{t.setAttributes({hasSeparator:e})}})),(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.ToggleControl,{label:"Show Seperator Y",help:"Adds a border to the bottom of the item. Great for objects stacked in a column.",checked:t.attributes.hasSeparatorY,onChange:e=>{t.setAttributes({hasSeparatorY:e})}})),(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.ToggleControl,{label:"Stack On Smaller Screens",help:"Show items stacked on smaller screens. Adds .is-stacked class to the element.",checked:t.attributes.isStackedMobile,onChange:e=>{t.setAttributes({isStackedMobile:e})}})))))),"withInspectorControl");(0,c.addFilter)("blocks.registerBlockType","waw-blocks/add-separator-attribute",(e,t)=>("core/group"!==t||(e.attributes={...e.attributes,hasSeparator:{type:"boolean",default:!1},hasSeparatorY:{type:"boolean",default:!1},isStackedMobile:{type:"boolean",default:!1}}),e)),(0,c.addFilter)("editor.BlockEdit","waw-block/add-separator-inspector-control",g),(0,c.addFilter)("blocks.getSaveElement","waw-blocks/add-separator-class-on-save",(e,t,o)=>{if("core/group"!==t.name)return e;const{hasSeparator:l,hasSeparatorY:r,isStackedMobile:n}=o;if(r||l||n){let t=[];return l&&t.push("is-show-seperator"),r&&t.push("is-show-seperator-y"),n&&t.push("is-stacked"),{...e,props:{...e.props,className:[e.props.className,...t].filter(Boolean).join(" ")}}}return e});const h=(0,s.createHigherOrderComponent)(e=>t=>"core/post-title"!==t.name?(0,u.createElement)(e,{...t}):(0,u.createElement)(m.Fragment,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(i.Panel,null,(0,u.createElement)(i.PanelBody,{title:"Title Settings",icon:b,initialOpen:!0},(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.ToggleControl,{label:"Use White Panel Style",checked:t.attributes.whitePanel,onChange:e=>{t.setAttributes({whitePanel:e})}})),(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.ToggleControl,{label:"Add Side Border (left)",checked:t.attributes.sideBorderLeft,onChange:e=>{t.setAttributes({sideBorderLeft:e})}})))))),"withInspectorControl");(0,c.addFilter)("blocks.registerBlockType","waw-blocks/add-title-attribute",(e,t)=>("core/post-title"!==t||(e.attributes={...e.attributes,whitePanel:{type:"boolean",default:!1},sideBorderLeft:{type:"boolean",default:!1}}),e)),(0,c.addFilter)("editor.BlockEdit","waw-block/add-title-inspector-control",h),(0,e.registerBlockStyle)("core/table",[{name:"plain",label:"Plain"}]);const w=(0,s.createHigherOrderComponent)(e=>t=>"core/cover"!==t.name?(0,u.createElement)(e,{...t}):(0,u.createElement)(m.Fragment,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(i.Panel,null,(0,u.createElement)(i.PanelBody,{title:"Extra Settings",icon:b,initialOpen:!0},(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.ToggleControl,{label:"Stretch to fill",help:"Make the cover height:100%. If used with columns block, make sure that single column is set to stretch to fill.",checked:t.attributes.stretchToFill,onChange:e=>{t.setAttributes({stretchToFill:e})}})))))),"withInspectorControl");(0,c.addFilter)("blocks.registerBlockType","waw-blocks/add-stretch-to-fill-attribute",(e,t)=>("core/cover"!==t||(e.attributes={...e.attributes,stretchToFill:{type:"boolean",default:!1}}),e)),(0,c.addFilter)("editor.BlockEdit","waw-block/add-stretch-to-fill-inspector-control",w),(0,c.addFilter)("blocks.getSaveElement","waw-blocks/add-stretch-to-fill-class-on-save",(e,t,o)=>{if("core/cover"!==t.name)return e;const{stretchToFill:l}=o;if(l){let t=[];return t.push("is-stretch-to-fill"),{...e,props:{...e.props,className:[e.props.className,...t].filter(Boolean).join(" ")}}}return e}),(0,e.registerBlockStyle)("core/gallery",[{name:"stack",label:"Stack"}]);const y=(0,s.createHigherOrderComponent)(e=>t=>"core/buttons"!==t.name?(0,u.createElement)(e,{...t}):(0,u.createElement)(m.Fragment,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(i.Panel,null,(0,u.createElement)(i.PanelBody,{title:"Extra Settings",icon:b,initialOpen:!0},(0,u.createElement)(i.BaseControl,null,(0,u.createElement)(i.RangeControl,{label:(0,p.__)("Column Gap"),value:t.attributes.gapSize,onChange:e=>t.setAttributes({gapSize:e}),min:0,max:10,step:1,required:!0,help:"Specify the size of the flex column gap. Each number corresponds to a CSS class incremented by 10px, but you can also set a custom CSS size. 1 = gap: 10px"})))))),"withInspectorControl");(0,c.addFilter)("blocks.registerBlockType","waw-blocks/add-gap-size-attribute",(e,t)=>("core/buttons"!==t||(e.attributes={...e.attributes,gapSize:{type:"number",default:0}}),e)),(0,c.addFilter)("editor.BlockEdit","waw-block/add-gap-size-inspector-control",y),(0,c.addFilter)("blocks.getSaveElement","waw-blocks/add-gap-size-class-on-save",(e,t,o)=>{if("core/buttons"!==t.name)return e;const{gapSize:l}=o;if(l){let t=[];return t.push(`is-column-gap-${l}`),{...e,props:{...e.props,className:[e.props.className,...t].filter(Boolean).join(" ")}}}return e});const k=(0,s.createHigherOrderComponent)(e=>t=>{if("core/button"!==t.name)return(0,u.createElement)(e,{...t});const{attributes:o,setAttributes:l}=t,{iconId:r,iconUrl:n,iconPosition:c,iconSize:s,iconSpacing:d}=o,g=()=>{l({iconId:0,iconUrl:""})};return(0,u.createElement)(m.Fragment,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(i.Panel,null,(0,u.createElement)(i.PanelBody,{title:"Button Icon",icon:b,initialOpen:!1},(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.BaseControl,{label:"Icon"},(0,u.createElement)(a.MediaUploadCheck,null,(0,u.createElement)(a.MediaUpload,{onSelect:e=>{l({iconId:e.id,iconUrl:e.url})},allowedTypes:["image"],value:r,render:({open:e})=>(0,u.createElement)("div",null,n?(0,u.createElement)("div",{style:{marginBottom:"10px"}},(0,u.createElement)("img",{src:n,alt:"Selected icon",style:{maxWidth:"50px",maxHeight:"50px",display:"block",marginBottom:"8px"}}),(0,u.createElement)(i.Button,{onClick:e,variant:"secondary",style:{marginRight:"8px"}},(0,p.__)("Change Icon")),(0,u.createElement)(i.Button,{onClick:g,variant:"secondary",isDestructive:!0},(0,p.__)("Remove Icon"))):(0,u.createElement)(i.Button,{onClick:e,variant:"secondary"},(0,p.__)("Select Icon")))})))),n&&(0,u.createElement)(m.Fragment,null,(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.SelectControl,{label:"Icon Position",value:c,options:[{label:"Left",value:"left"},{label:"Right",value:"right"}],onChange:e=>l({iconPosition:e})})),(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.SelectControl,{label:"Icon Size",value:s,options:[{label:"12px",value:"12px"},{label:"14px",value:"14px"},{label:"16px",value:"16px"},{label:"18px",value:"18px"},{label:"20px",value:"20px"},{label:"24px",value:"24px"},{label:"32px",value:"32px"}],onChange:e=>l({iconSize:e})})),(0,u.createElement)(i.PanelRow,null,(0,u.createElement)(i.SelectControl,{label:"Icon Spacing",value:d,options:[{label:"4px",value:"4px"},{label:"6px",value:"6px"},{label:"8px",value:"8px"},{label:"10px",value:"10px"},{label:"12px",value:"12px"},{label:"16px",value:"16px"}],onChange:e=>l({iconSpacing:e})})))))))},"withIconControls");(0,c.addFilter)("blocks.registerBlockType","waw-blocks/add-button-icon-attributes",(e,t)=>("core/button"!==t||(e.attributes={...e.attributes,iconId:{type:"number",default:0},iconUrl:{type:"string",default:""},iconPosition:{type:"string",default:"left"},iconSize:{type:"string",default:"16px"},iconSpacing:{type:"string",default:"8px"}}),e)),(0,c.addFilter)("editor.BlockEdit","waw-blocks/add-button-icon-controls",k),(0,c.addFilter)("blocks.getSaveElement","waw-blocks/add-button-icon-save",(e,t,o)=>{if("core/button"!==t.name)return e;const{iconUrl:l,iconPosition:r,iconSize:n,iconSpacing:a}=o;if(!l)return e;const i={type:"img",props:{src:l,alt:"",className:"wp-button-icon",style:{width:n,height:n,display:"inline-block",verticalAlign:"middle",..."left"===r?{marginRight:a}:{marginLeft:a}}}},c={...e};if(c.props&&c.props.children){const e=Array.isArray(c.props.children)?[...c.props.children]:[c.props.children];"left"===r?e.unshift(i):e.push(i),c.props={...c.props,children:e}}return c}),o(38);const{__}=wp.i18n,{PanelBody:E}=wp.components,{addFilter:f}=wp.hooks,{Fragment:S}=wp.element;f("blocks.registerBlockType","my-custom-query-extension/add-gap-size-attributes",(e,t)=>("core/query"!==t||(e.attributes={...e.attributes,rowGap:{type:"number",default:0},columnGap:{type:"number",default:0},groupByCategory:{type:"boolean",default:!1}}),e)),f("editor.BlockEdit","my-custom-query-extension/gap-size-control",e=>t=>{if("core/query"!==t.name)return(0,u.createElement)(e,{...t});const{attributes:o,setAttributes:l}=t,{rowGap:r,columnGap:n}=o;return(0,u.createElement)(S,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(E,{title:__("Gap Sizes","text-domain")},(0,u.createElement)(i.RangeControl,{label:__("Row Gap (0 - 100)","text-domain"),value:r,onChange:e=>l({rowGap:e}),min:0,max:100,step:10}),(0,u.createElement)(i.RangeControl,{label:__("Column Gap (0 - 100)","text-domain"),value:n,onChange:e=>l({columnGap:e}),min:0,max:100,step:10}))))}),f("editor.BlockEdit","my-plugin/sort-by-category-group",e=>t=>{if("core/query"!==t.name)return(0,u.createElement)(e,{...t});const{attributes:o,setAttributes:l}=t;return(0,u.createElement)(S,null,(0,u.createElement)(e,{...t}),(0,u.createElement)(a.InspectorControls,null,(0,u.createElement)(E,{title:"Query Settings"},(0,u.createElement)(i.ToggleControl,{label:"Group by Category",checked:o.groupByCategory,onChange:e=>l({groupByCategory:e})}))))}),"undefined"==typeof window||window.wp?.blocks||(()=>{const e=document.createElement("script");e.innerHTML="\n\t\tdocument.addEventListener('DOMContentLoaded', function() {\n\t\t\t// Fix image blocks with padding\n\t\t\tconst imageBlocks = document.querySelectorAll('.wp-block-image[style*=\"padding\"]');\n\n\t\t\timageBlocks.forEach(function(figure) {\n\t\t\t\tconst img = figure.querySelector('img');\n\t\t\t\tif (!img) return;\n\n\t\t\t\tconst imgStyle = img.getAttribute('style') || '';\n\n\t\t\t\t// Extract shadow from img\n\t\t\t\tconst shadowMatch = imgStyle.match(/box-shadow:\\s*([^;]+)/);\n\t\t\t\tif (shadowMatch) {\n\t\t\t\t\tconst shadowValue = shadowMatch[1];\n\t\t\t\t\tfigure.style.boxShadow = shadowValue;\n\t\t\t\t\timg.style.boxShadow = 'none';\n\t\t\t\t}\n\n\t\t\t\t// Extract border radius from img\n\t\t\t\tconst radiusMatch = imgStyle.match(/border-radius:\\s*([^;]+)/);\n\t\t\t\tif (radiusMatch) {\n\t\t\t\t\tconst radiusValue = radiusMatch[1];\n\t\t\t\t\tfigure.style.borderRadius = radiusValue;\n\t\t\t\t\timg.style.borderRadius = '0';\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t",document.head.appendChild(e)})(),(0,c.addFilter)("blocks.registerBlockType","waw-blocks/enable-image-supports",(e,t)=>"core/image"!==t?e:{...e,supports:{...e.supports,border:{color:!0,radius:!0,style:!0,width:!0},spacing:{padding:!0,margin:!0}}}),(0,c.addFilter)("blocks.getSaveElement","waw-blocks/fix-image-shadow-placement",(e,t,o)=>{if("core/image"!==t.name)return e;const l=o.style?.spacing?.padding,r=o.style?.shadow;if(l&&r){const t={...e.props.style,boxShadow:r},o=e.props.children;if(o&&o.props){const l={...o.props.style};return delete l.boxShadow,{...e,props:{...e.props,style:t,children:{...o,props:{...o.props,style:l}}}}}}return e}),new function(){wp.domReady(function(){(0,e.registerBlockStyle)("core/columns",[{name:"no-gaps",label:"Columns no gaps"},{name:"20-gap",label:"20px Gap"},{name:"50-gap",label:"50px Gap"},{name:"80-gap",label:"80px Gap"},{name:"135-gap",label:"135px Gap"}]),(0,e.registerBlockStyle)("core/query",[{name:"two-columns",label:"Two Columns"},{name:"three-columns",label:"Three Columns"},{name:"list-view",label:"List View"}]);const t="waw-blocks/course-list";(0,e.registerBlockVariation)("core/query",{name:t,title:"WAW Courses List",description:"Displays a list of courses",isActive:({namespace:e,query:o})=>e===t&&"course"===o.postType,icon:r,attributes:{namespace:t,query:{perPage:6,pages:0,offset:0,postType:"course",order:"desc",orderBy:"date",author:"",search:"",exclude:[],sticky:"",inherit:!1}},scope:["inserter"],innerBlocks:[["core/post-template",{},[["core/post-title"],["core/post-excerpt"]]],["core/query-pagination"],["core/query-no-results"]]})})}})()})();