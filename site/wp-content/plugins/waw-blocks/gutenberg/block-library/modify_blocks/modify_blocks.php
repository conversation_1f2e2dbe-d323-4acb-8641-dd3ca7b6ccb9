<?php

/**
 * Modify the main query for post type archives to group posts by category
 * when the 'groupByCategory' attribute is set to true.
 *
 * This function hooks into the 'pre_get_posts' action, allowing us to
 * customize the query before it retrieves posts from the database.
 * By checking if we are on the front end and specifically on a post
 * type archive, we can ensure that our modifications only apply
 * in the appropriate context.
 *
 * If the 'groupByCategory' parameter is present and set to true,
 * the query will be adjusted to order posts first by category name
 * and then by title, ensuring a clear and organized display of posts
 * within each category.
 */
add_filter('pre_render_block', 'myplugin_pre_render_block', 10, 2);

function myplugin_pre_render_block($pre_render, $parsed_block)
{

	// Determine if this is the custom block variation.
	if (
		isset($parsed_block['blockName'], $parsed_block['attrs']['groupByCategory']) &&
		'core/query' === $parsed_block['blockName'] &&
		true === $parsed_block['attrs']['groupByCategory']
	) {
		// var_dump($parsed_block['attrs']['groupByCategory']);

		add_filter(
			'query_loop_block_query_vars',
			function ($query, $block) use ($parsed_block) {

				// Ensure posts are grouped by category and sorted within each category by menu_order.
				$query['orderby'] = [
					'category' => 'ASC',    // First, sort by category
					'menu_order' => 'ASC'   // Then, sort by menu order within each category
				];
				$query['order'] = 'ASC';

				// If a star rating filter is specified in the block attributes, add it to the query.
				if (isset($parsed_block['attrs']['query']['starRating'])) {
					$query['meta_key']   = 'rating';
					$query['meta_value'] = absint($parsed_block['attrs']['query']['starRating']);
				}

				return $query;
			},
			10,
			2
		);
	}

	return $pre_render;
}


// Gap Size Control
function my_custom_gap_size_class($block_content, $block)
{
	// Check if it's the core/query block
	if ($block['blockName'] === 'core/query') {
		$row_gap = isset($block['attrs']['rowGap']) ? esc_attr($block['attrs']['rowGap']) : 0;
		$column_gap = isset($block['attrs']['columnGap']) ? esc_attr($block['attrs']['columnGap']) : 0;

		// Generate classes for rowGap and columnGap
		$row_gap_class = $row_gap ? "has-row-gap-$row_gap" : '';
		$column_gap_class = $column_gap ? "has-column-gap-$column_gap" : '';

		// Combine classes
		$class = trim("$row_gap_class $column_gap_class");

		// Apply the classes to the wrapper
		if ($class) {
			$block_content = str_replace('wp-block-query', 'wp-block-query ' . $class, $block_content);
		}
	}

	return $block_content; // Ensure this is returning a string
}

add_filter('render_block', 'my_custom_gap_size_class', 10, 2);


/*
 * Modifys a number of built-in blocks with additional styles
 */
function waw_modify_blocks_enqueue()
{
	wp_enqueue_script(
		'blocks-mod-editor-script',
		plugin_dir_url(__FILE__) . 'build/index.build.js',
		array('wp-blocks', 'wp-dom-ready', 'wp-editor', 'wp-element', 'wp-components'),
		'1.0.0'
	);
	wp_enqueue_style(
		'blocks-mod-editor-style',
		plugin_dir_url(__FILE__) . 'build/frontend.css',
		array(),
		'1.0.0'
	);
}

function enqueue_frontend_assets()
{
	wp_enqueue_style(
		'blocks-mod-style',
		plugin_dir_url(__FILE__) . 'build/frontend.css'
	);
}
add_action('enqueue_block_editor_assets', 'waw_modify_blocks_enqueue', 6);
add_action('wp_enqueue_scripts', 'enqueue_frontend_assets', 6);

// Menublock

function my_render_navigation_submenu_block($block_content, $block)
{

	if ($block['blockName'] !== 'core/navigation-link') {
		return $block_content;
	}

	// var_dump($block['attrs']['label']);

	// Check if the reusableBlockId attribute is set
	if (!empty($block['attrs']['reusableBlockId']) && $block['attrs']['reusableBlockId'] > 0) {
		$reusable_block_id = $block['attrs']['reusableBlockId'];

		// Get the post content of the reusable block
		$reusable_block_post = get_post($reusable_block_id);

		// var_dump($reusable_block_post);
		if ($reusable_block_post) {
			// Apply 'the_content' filter to process shortcodes, etc.
			$reusable_block_content = apply_filters('the_content', $reusable_block_post->post_content);

			// Append the processed content within the existing structure
			$block_content .= '<li class=" wp-block-navigation-item wp-block-navigation-link"><div class="wawNavContainer">' . $reusable_block_content . '</div></li>';
		}
	}

	return $block_content;
}

add_filter('render_block', 'my_render_navigation_submenu_block', 10, 2);

function my_custom_post_title_block_render($block_content, $block)
{
	if ('core/post-title' === $block['blockName'] && !empty($block['attrs']['whitePanel']) && $block['attrs']['whitePanel']) {

		$additionalClasses = [];
		$additionalClasses[] = 'withWhitePanel';

		if (!empty($block['attrs']['sideBorderLeft']) && $block['attrs']['sideBorderLeft']) {
			$additionalClasses[] = 'withSideBorder';
		}

		// Check if the block has a specific attribute or class, or just add your changes directly.
		// For example, appending an SVG
		$svg = '<svg width="15" height="26" viewBox="0 0 15 26" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path d="M2 24C2 24 13 16.4195 13 12.9968C13 9.57419 2 2 2 2" stroke="#D45E2B" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
		</svg>
		';
		$block_content .= $svg;

		// Get the current post ID and its permalink
		$post_id = get_the_ID();
		$permalink = get_permalink($post_id);

		// Check if permalink is available
		if ($permalink) {
			// Wrap the block content inside an <a> tag with the permalink
			$block_content = '<a href="' . esc_url($permalink) . '">' . $block_content . '</a>';
		}

		// Use the implode() function to convert the array into a comma-separated string
		// The first parameter of implode() is the separator (", ") used between array elements
		$additionalClassesString = implode(" ", $additionalClasses);

		// Add a custom class
		$block_content = '<div class="' . $additionalClassesString . '">' . $block_content . '</div>';
	}

	return $block_content;
}
add_filter('render_block', 'my_custom_post_title_block_render', 10, 2);
