(()=>{var e={20:(e,t,n)=>{"use strict";var o=n(609),l=Symbol.for("react.element"),a=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),r=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,n){var o,i={},s=null,u=null;for(o in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(u=t.ref),t)a.call(t,o)&&!c.hasOwnProperty(o)&&(i[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===i[o]&&(i[o]=t[o]);return{$$typeof:l,type:e,key:s,ref:u,props:i,_owner:r.current}}},609:e=>{"use strict";e.exports=window.React},848:(e,t,n)=>{"use strict";e.exports=n(20)},942:(e,t)=>{var n;!function(){"use strict";var o={}.hasOwnProperty;function l(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=r(e,a(n)))}return e}function a(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return l.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)o.call(e,n)&&e[n]&&(t=r(t,n));return t}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(l.default=l,e.exports=l):void 0===(n=function(){return l}.apply(t,[]))||(e.exports=n)}()}},t={};function n(o){var l=t[o];if(void 0!==l)return l.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=window.wp.i18n,t=window.wp.primitives;var o=n(848);const l=(0,o.jsx)(t.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)(t.Path,{d:"M13 6v6h5.2v4c0 .8-.2 1.4-.5 1.7-.6.6-1.6.6-2.5.5h-.3v1.5h.5c1 0 2.3-.1 3.3-1 .6-.6 1-1.6 1-2.8V6H13zm-9 6h5.2v4c0 .8-.2 1.4-.5 1.7-.6.6-1.6.6-2.5.5h-.3v1.5h.5c1 0 2.3-.1 3.3-1 .6-.6 1-1.6 1-2.8V6H4v6z"})}),a=window.wp.blocks;var r=n(609);const c=window.wp.components,i=window.wp.apiFetch;var s=n.n(i);const u=window.wp.blockEditor,m=(0,o.jsx)(t.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)(t.Path,{d:"M4 9v1.5h16V9H4zm12 5.5h4V13h-4v1.5zm-6 0h4V13h-4v1.5zm-6 0h4V13H4v1.5z"})});var d=n(942),p=n.n(d);const w=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"waw-blocks/svg-icon","title":"WAW SVG Icon","category":"text","description":"Give quoted text visual emphasis. \\"In quoting others, we cite ourselves.\\" — Julio Cortázar","keywords":["icon","waw"],"textdomain":"default","attributes":{"selectedIcon":{"type":"string","default":""},"iconSize":{"type":"string","default":"32px"},"iconColor":{"type":"string","default":""},"iconColorHover":{"type":"string","default":""},"colorMode":{"type":"string","default":"auto"},"url":{"type":"string","default":""},"urlNewWindow":{"type":"boolean","default":false}},"supports":{"anchor":true,"html":false,"layout":{"allowEditing":false},"spacing":{"padding":true,"margin":true,"blockGap":true}},"styles":[{"name":"default","label":"Default","isDefault":true},{"name":"plain","label":"Plain"}],"editorScript":"file:../build/index.build.js","editorStyle":"file:../build/index.css"}'),{name:v}=w;!function(e){if(!e)return;const{metadata:t,settings:n,name:o}=e;(0,a.registerBlockType)({name:o,...t},n)}({name:v,metadata:w,settings:{icon:l,attributes:{...w.attributes},edit:function(t){const[n,o]=(0,r.useState)(""),[l,a]=(0,r.useState)([]),[i,d]=(0,r.useState)([]),[w,v]=(0,r.useState)(1),[g,h]=(0,r.useState)(1),{attributes:f,setAttributes:C}=t,{iconSize:E,iconColor:S,iconColorHover:b,selectedIcon:y,url:_,urlNewWindow:x,colorMode:I}=f,k=(0,u.useBlockProps)(),P={...k,className:p()(k.className,"wawSvgIcon")},N=(0,r.useCallback)((e,t,n)=>{const o=n.length>0?`&folders=${encodeURIComponent(n.join(","))}`:"";s()({path:`/waw-blocks/v1/svg-icons/?search=${encodeURIComponent(e)}${o}&page=${t}`}).then(e=>{d(e.icons);const t=Math.ceil(e.total/64);h(t>0?t:1)})},[]),R=(0,r.useCallback)((()=>{let e;return function(...t){clearTimeout(e),e=setTimeout(()=>{clearTimeout(e),(e=>{v(1),N(e,1,l)})(...t)},1e3)}})(),[N]);return(0,r.useEffect)(()=>{""!==n&&R(n)},[n,R]),(0,r.useEffect)(()=>{""!==n&&N(n,w,l)},[l,w]),(0,r.createElement)(r.Fragment,null,(0,r.createElement)(u.InspectorControls,null,(0,r.createElement)(c.Panel,null,(0,r.createElement)(c.PanelBody,{title:"Color Settings",icon:m,initialOpen:!0},(0,r.createElement)(c.PanelRow,null,(0,r.createElement)(c.SelectControl,{label:"Color Mode",value:I,options:[{label:"Auto (Detect currentColor)",value:"auto"},{label:"CSS Color Property",value:"color"},{label:"SVG Fill & Stroke",value:"fill-stroke"}],onChange:e=>C({colorMode:e}),help:"Choose how colors are applied to the SVG icon"}))),(0,r.createElement)(u.PanelColorSettings,{title:"Icon Color",colorSettings:[{value:S,onChange:e=>C({iconColor:e}),label:"Icon Color"},{value:b,onChange:e=>C({iconColorHover:e}),label:"Icon Color (Hover)"}]}),(0,r.createElement)(c.PanelBody,{title:"Icon Settings",icon:m,initialOpen:!0},f.selectedIcon&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)(c.PanelRow,null,"Current Icon"),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)("img",{className:"wawCard__image-element",style:{maxWidth:"24px"},src:f.selectedIcon})),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)(c.Button,{variant:"secondary",size:"small",className:"block-library-cover__reset-button",onClick:()=>C({selectedIcon:void 0})},(0,e.__)("Clear Media")))),(0,r.createElement)(u.MediaUploadCheck,null,(0,r.createElement)(u.MediaUpload,{value:y,onSelect:e=>{let t=e.sizes.full.url;void 0!==e.sizes.medium&&(t=e.sizes.medium.url),C({selectedIcon:t})},type:"image",render:e=>(0,r.createElement)(c.Button,{onClick:e.open,className:"components-button editor-post-publish-button editor-post-publish-button__button is-primary"},"Select icon image")})),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)("div",{className:"wawSvgIcon"},(0,r.createElement)("div",{className:"wawSvgIcon__search",style:{marginBottom:"20px"}},(0,r.createElement)(c.TextControl,{label:"Search Icon",value:n,onChange:e=>o(e)})),(0,r.createElement)("div",{className:"wawSvgIcon__categories",style:{marginBottom:"20px"}},["brands","light","regular","solid","thin"].map(e=>(0,r.createElement)(c.CheckboxControl,{key:e,label:e.charAt(0).toUpperCase()+e.slice(1),checked:l.includes(e),onChange:()=>(e=>{a(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])})(e)}))),t.isSelected&&(0,r.createElement)("div",{className:"wawSvgIcon__wrapper"},n&&(i&&i.length>0?i.map(e=>(0,r.createElement)("a",{className:f.selectedIcon===e.path?"wawSvgIcon__icon wawSvgIcon__icon--isSelected":"wawSvgIcon__icon",onClick:()=>C({selectedIcon:e.path})},(0,r.createElement)("img",{key:e.name,src:e.path,className:"wawSvgIcon__icon-img"}),(0,r.createElement)("p",null,e.name))):"Nothing found")),n&&t.isSelected&&(0,r.createElement)("div",{style:{display:"flex",justifyContent:"space-between",marginTop:"20px"}},(0,r.createElement)(c.Button,{variant:"secondary",onClick:()=>v(w-1),disabled:1===w},"Previous"),(0,r.createElement)("div",{className:"wawSvgIcon__pagination"},(0,r.createElement)("div",{className:"wawSvgIcon__pagination-current"},w),(0,r.createElement)("div",{className:"wawSvgIcon__pagination-of"},"of"),(0,r.createElement)("div",{className:"wawSvgIcon__pagination-max"},g)),(0,r.createElement)(c.Button,{variant:"secondary",onClick:()=>v(w+1),disabled:w===g},"Next")))),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)(c.__experimentalUnitControl,{label:"Width",onChange:e=>C({iconSize:e||""}),value:E,units:[{value:"px",label:"px",default:32}],allowReset:!0,resetFallbackValue:"",placeholder:"Auto"})),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)("p",null,"Icon width and height. Leave empty for auto sizing.")),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)(c.TextControl,{label:"Link",value:_,onChange:e=>C({url:e})})),(0,r.createElement)(c.PanelRow,null,(0,r.createElement)(c.ToggleControl,{label:"Open in a new window",checked:x,onChange:e=>{C({urlNewWindow:e})}}))))),(0,r.createElement)("div",{...P},f.selectedIcon?(0,r.createElement)("img",{className:"wawCard__image-element",src:f.selectedIcon}):(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},(0,r.createElement)("path",{d:"M152.014 120C125.504 120 104.014 141.492 104.014 168S125.504 216 152.014 216S200.014 194.508 200.014 168S178.523 120 152.014 120ZM448 32H64C28.654 32 0 60.652 0 96V416C0 451.346 28.654 480 64 480H448C483.348 480 512 451.346 512 416V96C512 60.652 483.348 32 448 32ZM464 409.303L327.229 223.379C323.838 218.768 318.129 216 312.014 216C305.9 216 300.191 218.768 296.801 223.379L190.166 368.338L153.074 322.236C149.633 317.957 144.141 315.428 138.301 315.428C132.459 315.428 126.967 317.957 123.525 322.236L48.018 416.084C48.018 416.055 48 416.029 48 416V96C48 87.178 55.178 80 64 80H448C456.822 80 464 87.178 464 96V409.303Z"}))))},save:function(e){return null}}})})()})();