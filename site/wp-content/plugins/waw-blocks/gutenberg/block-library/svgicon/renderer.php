<?php

namespace WAWBlocks\SvgIcon;

class Renderer
{
	/**
	 * Custom SVG sanitization that preserves clipPath elements
	 */
	private function sanitize_svg_content($svg_content, $allowed_svg_elements)
	{
		// First try standard wp_kses
		$sanitized = wp_kses($svg_content, $allowed_svg_elements);

		// Check if clipPath was stripped and original had it
		if (strpos($svg_content, '<clipPath') !== false && strpos($sanitized, '<clipPath') === false) {
			// Try a more permissive approach for clipPath specifically
			$svg_with_clippath = $svg_content;

			// Replace clipPath with a temporary placeholder that wp_kses won't strip
			$svg_with_clippath = preg_replace('/<clipPath([^>]*)>/i', '<clippath$1>', $svg_with_clippath);
			$svg_with_clippath = preg_replace('/<\/clipPath>/i', '</clippath>', $svg_with_clippath);

			// Add clippath (lowercase) to allowed elements temporarily
			$temp_allowed = $allowed_svg_elements;
			$temp_allowed['clippath'] = $allowed_svg_elements['clipPath'];

			// Sanitize with lowercase clippath
			$sanitized = wp_kses($svg_with_clippath, $temp_allowed);

			// Convert back to proper clipPath
			$sanitized = preg_replace('/<clippath([^>]*)>/i', '<clipPath$1>', $sanitized);
			$sanitized = preg_replace('/<\/clippath>/i', '</clipPath>', $sanitized);
		}

		return $sanitized;
	}

	public function render_callback($block_attributes, $content)
	{
		$iconColor = $block_attributes['iconColor'] ?? '#000';
		$iconColorHover = $block_attributes['iconColorHover'] ?? '#000';
		$iconSize = $block_attributes['iconSize'] ?? '32px';
		$colorMode = $block_attributes['colorMode'] ?? 'auto';
		$site_url = site_url('/');
		$svg_url = $block_attributes['selectedIcon'];

		// Handle both full URLs and relative paths
		if (strpos($svg_url, $site_url) !== false) {
			// Full URL - convert to relative path
			$svg_url = str_replace($site_url, '', $svg_url);
		}

		// Check if this is a plugin asset path
		$plugin_path_base = plugin_dir_path(__FILE__);

		if (strpos($svg_url, 'wp-content/plugins/waw-blocks/gutenberg/block-library/svgicon/assets/svg/') !== false) {
			// This is a plugin SVG asset - construct the correct file path
			$relative_svg_path = str_replace('wp-content/plugins/waw-blocks/gutenberg/block-library/svgicon/assets/svg/', 'assets/svg/', $svg_url);
			$svg_path = $plugin_path_base . $relative_svg_path;
		} else {
			// Fallback to ABSPATH for other paths
			// Remove leading slash if present to avoid double slashes
			$svg_url = ltrim($svg_url, '/');
			$svg_path = ABSPATH . $svg_url;
		}


		$style = "style='max-width: " . esc_attr($iconSize) . ";";

		$styleAttributes = $block_attributes['style']['spacing'] ?? [];

		foreach (['margin', 'padding'] as $spacingType) {
			foreach (['top', 'right', 'bottom', 'left'] as $direction) {
				if (isset($styleAttributes[$spacingType][$direction])) {
					$value = $styleAttributes[$spacingType][$direction];
					if (strpos($value, 'var:preset|spacing|') === 0) {
						$number = str_replace('var:preset|spacing|', '', $value);
						$cssVarName = "--wp--preset--spacing--$number";
						$style .= " {$spacingType}-{$direction}: var({$cssVarName});";
					} else {
						$style .= " {$spacingType}-{$direction}: " . esc_attr($value) . ";";
					}
				}
			}
		}

		$style .= "'";

		if (file_exists($svg_path) && is_readable($svg_path) && pathinfo($svg_path, PATHINFO_EXTENSION) === 'svg') {
			$svg_content = file_get_contents($svg_path);

			if (strpos($svg_content, '<svg') !== false && strpos($svg_content, '</svg>') !== false) {

				$block_identifier = 'block-' . uniqid();

				$allowed_svg_elements = array(
					'svg'   => array(
						'class'           => true,
						'aria-hidden'     => true,
						'aria-labelledby' => true,
						'role'            => true,
						'xmlns'           => true,
						'xmlns:xlink'     => true,
						'width'           => true,
						'height'          => true,
						'viewBox'         => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'id'              => true,
					),
					'g'     => array(
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'clip-path'       => true,
						'id'              => true,
						'class'           => true,
						'transform'       => true,
					),
					'title' => array('title' => true),
					'path'  => array(
						'd'               => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'stroke-linecap'  => true,
						'stroke-linejoin' => true,
						'stroke-dasharray' => true,
						'stroke-dashoffset' => true,
						'stroke-opacity'  => true,
						'fill-opacity'    => true,
						'opacity'         => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'rect' => array(
						'width'           => true,
						'height'          => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'stroke-linecap'  => true,
						'stroke-linejoin' => true,
						'x'               => true,
						'y'               => true,
						'rx'              => true,
						'ry'              => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'circle' => array(
						'cx'              => true,
						'cy'              => true,
						'r'               => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'stroke-linecap'  => true,
						'stroke-linejoin' => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'ellipse' => array(
						'cx'              => true,
						'cy'              => true,
						'rx'              => true,
						'ry'              => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'line' => array(
						'x1'              => true,
						'y1'              => true,
						'x2'              => true,
						'y2'              => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'stroke-linecap'  => true,
						'stroke-linejoin' => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'polyline' => array(
						'points'          => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'stroke-linecap'  => true,
						'stroke-linejoin' => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'polygon' => array(
						'points'          => true,
						'fill'            => true,
						'stroke'          => true,
						'stroke-width'    => true,
						'stroke-linecap'  => true,
						'stroke-linejoin' => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'defs' => array(
						'id'              => true,
					),
					'clipPath' => array(
						'id'              => true,
						'clipPathUnits'   => true,
					),
					'pattern' => array(
						'id'                 => true,
						'patternUnits'       => true,
						'patternContentUnits' => true,
						'width'              => true,
						'height'             => true,
						'x'                  => true,
						'y'                  => true,
						'viewBox'            => true,
						'patternTransform'   => true,
					),
					'use' => array(
						'xlink:href' => true,
						'href'       => true,
						'transform'  => true,
						'x'          => true,
						'y'          => true,
						'width'      => true,
						'height'     => true,
						'id'         => true,
						'class'      => true,
					),
					'image' => array(
						'id'         => true,
						'width'      => true,
						'height'     => true,
						'x'          => true,
						'y'          => true,
						'xlink:href' => true,
						'href'       => true,
						'transform'  => true,
						'class'      => true,
					),
					'text' => array(
						'x'               => true,
						'y'               => true,
						'dx'              => true,
						'dy'              => true,
						'font-family'     => true,
						'font-size'       => true,
						'font-weight'     => true,
						'text-anchor'     => true,
						'fill'            => true,
						'stroke'          => true,
						'transform'       => true,
						'id'              => true,
						'class'           => true,
					),
					'tspan' => array(
						'x'               => true,
						'y'               => true,
						'dx'              => true,
						'dy'              => true,
						'font-family'     => true,
						'font-size'       => true,
						'font-weight'     => true,
						'text-anchor'     => true,
						'fill'            => true,
						'stroke'          => true,
						'id'              => true,
						'class'           => true,
					),
				);


				// Use custom SVG sanitization that preserves clipPath
				$sanitized_svg_content = $this->sanitize_svg_content($svg_content, $allowed_svg_elements);

				if (!empty($block_attributes['url'])) {
					$url = esc_url($block_attributes['url']);
					$a_tag = "<a href='{$url}'";

					if (isset($block_attributes['urlNewWindow']) && $block_attributes['urlNewWindow'] === true) {
						$a_tag .= ' target="_blank" rel="noopener noreferrer"';
					}

					$a_tag .= ">{$sanitized_svg_content}</a>";
					$sanitized_svg_content = $a_tag;
				}

				$html = '';
				if ($iconColor) {
					$html .= "<style>";

					// Determine color mode
					$uses_current_color = strpos($sanitized_svg_content, 'currentColor') !== false;
					$effective_color_mode = $colorMode;

					// Auto-detect if mode is set to auto
					if ($colorMode === 'auto') {
						$effective_color_mode = $uses_current_color ? 'color' : 'fill-stroke';
					}

					if ($effective_color_mode === 'color') {
						// Set CSS color property on the container (works with currentColor)
						$html .= "#{$block_identifier} { color: " . esc_attr($iconColor) . "; transition: all 0.3s ease; }";
						$html .= "#{$block_identifier}:hover { color: " . esc_attr($iconColorHover) . "; }";
					} else {
						// Set fill and stroke on the SVG element (traditional approach)
						$html .= "#{$block_identifier} svg { fill: " . esc_attr($iconColor) . "; stroke: " . esc_attr($iconColor) . "; transition: all 0.3s ease; }";
						$html .= "#{$block_identifier}:hover svg { fill: " . esc_attr($iconColorHover) . "; stroke: " . esc_attr($iconColorHover) . "; }";
					}

					$html .= "</style>";
				}
				$html .= "<div id='{$block_identifier}' class='wawSvgIcon";
				$html .= isset($block_attributes["className"]) ? " {$block_attributes["className"]}" : "";
				$html .= "' {$style}><div class='wawSvgIcon__wrapper'>{$sanitized_svg_content}</div></div>";


				return $html;
			}
		} else {
			return "<div class='my-svg-container'>" . esc_html__('SVG file not found.', 'your-text-domain') . "</div>";
		}
	}
}
