<?php

namespace WAWBlocks\SvgIcon;

class Renderer
{
	public function render_callback($block_attributes, $content)
	{
		$iconColor = $block_attributes['iconColor'] ?? '#000';
		$iconColorHover = $block_attributes['iconColorHover'] ?? '#000';
		$iconSize = $block_attributes['iconSize'] ?? '32px';
		$site_url = site_url('/');
		$svg_url = $block_attributes['selectedIcon'];

		// Handle both full URLs and relative paths
		if (strpos($svg_url, $site_url) !== false) {
			// Full URL - convert to relative path
			$svg_url = str_replace($site_url, '', $svg_url);
		}

		// Check if this is a plugin asset path
		$plugin_path_base = plugin_dir_path(__FILE__);

		if (strpos($svg_url, 'wp-content/plugins/waw-blocks/gutenberg/block-library/svgicon/assets/svg/') !== false) {
			// This is a plugin SVG asset - construct the correct file path
			$relative_svg_path = str_replace('wp-content/plugins/waw-blocks/gutenberg/block-library/svgicon/assets/svg/', 'assets/svg/', $svg_url);
			$svg_path = $plugin_path_base . $relative_svg_path;
		} else {
			// Fallback to ABSPATH for other paths
			// Remove leading slash if present to avoid double slashes
			$svg_url = ltrim($svg_url, '/');
			$svg_path = ABSPATH . $svg_url;
		}


		$style = "style='max-width: " . esc_attr($iconSize) . ";";

		$styleAttributes = $block_attributes['style']['spacing'] ?? [];

		foreach (['margin', 'padding'] as $spacingType) {
			foreach (['top', 'right', 'bottom', 'left'] as $direction) {
				if (isset($styleAttributes[$spacingType][$direction])) {
					$value = $styleAttributes[$spacingType][$direction];
					if (strpos($value, 'var:preset|spacing|') === 0) {
						$number = str_replace('var:preset|spacing|', '', $value);
						$cssVarName = "--wp--preset--spacing--$number";
						$style .= " {$spacingType}-{$direction}: var({$cssVarName});";
					} else {
						$style .= " {$spacingType}-{$direction}: " . esc_attr($value) . ";";
					}
				}
			}
		}

		$style .= "'";

		if (file_exists($svg_path) && is_readable($svg_path) && pathinfo($svg_path, PATHINFO_EXTENSION) === 'svg') {
			$svg_content = file_get_contents($svg_path);

			if (strpos($svg_content, '<svg') !== false && strpos($svg_content, '</svg>') !== false) {

				$block_identifier = 'block-' . uniqid();

				$allowed_svg_elements = array(
					'svg'   => array(
						'class'           => true,
						'aria-hidden'     => true,
						'aria-labelledby' => true,
						'role'            => true,
						'xmlns'           => true,
						'xmlns:xlink'     => true,
						'width'           => true,
						'height'          => true,
						'viewBox'         => true, // Corrected 'viewbox' to 'viewBox'
						'fill'            => true,
					),
					'g'     => array('fill' => true),
					'title' => array('title' => true),
					'path'  => array(
						'd'              => true,
						'fill'           => true,
						'stroke'         => true,
						'stroke-width'   => true,
						'stroke-linecap' => true,
						'stroke-linejoin' => true,
					),
					'rect' => array(
						'width'  => true,
						'height' => true,
						'fill'   => true,
					),
					'pattern' => array(
						'id'                 => true,
						'patterncontentunits' => true,
						'width'              => true,
						'height'             => true,
					),
					'use' => array(
						'xlink:href' => true,
						'transform'  => true,
					),
					'image' => array(
						'id'         => true,
						'width'      => true,
						'height'     => true,
						'xlink:href' => true,
					),
					'defs' => array(),
				);


				$sanitized_svg_content = wp_kses($svg_content, $allowed_svg_elements);

				if (!empty($block_attributes['url'])) {
					$url = esc_url($block_attributes['url']);
					$a_tag = "<a href='{$url}'";

					if (isset($block_attributes['urlNewWindow']) && $block_attributes['urlNewWindow'] === true) {
						$a_tag .= ' target="_blank" rel="noopener noreferrer"';
					}

					$a_tag .= ">{$sanitized_svg_content}</a>";
					$sanitized_svg_content = $a_tag;
				}

				$html = '';
				if ($iconColor) {
					$html .= "<style>";
					$html .= "#{$block_identifier} svg {fill: " . esc_attr($iconColor) . "; transition: all 0.3s ease}";
					$html .= "#{$block_identifier} svg:hover {fill: " . esc_attr($iconColorHover) . "}";
					$html .= "</style>";
				}
				$html .= "<div id='{$block_identifier}' class='wawSvgIcon";
				$html .= isset($block_attributes["className"]) ? " {$block_attributes["className"]}" : "";
				$html .= "' {$style}><div class='wawSvgIcon__wrapper'>{$sanitized_svg_content}</div></div>";


				return $html;
			}
		} else {
			return "<div class='my-svg-container'>" . esc_html__('SVG file not found.', 'your-text-domain') . "</div>";
		}
	}
}
