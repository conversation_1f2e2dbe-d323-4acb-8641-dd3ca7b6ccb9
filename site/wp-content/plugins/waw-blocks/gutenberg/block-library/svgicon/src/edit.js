import React, { useState, useEffect, useCallback, Fragment } from 'react';
import { Panel, PanelBody, PanelRow, TextControl, Button, CheckboxControl, ToggleControl, SelectControl } from '@wordpress/components';
import apiFetch from '@wordpress/api-fetch';
import { useBlockProps, InspectorControls, PanelColorSettings, MediaUploadCheck, MediaUpload } from '@wordpress/block-editor';
import { __experimentalUnitControl as UnitControl } from '@wordpress/components';
import { more } from '@wordpress/icons';

import { __ } from '@wordpress/i18n';
import classNames from 'classnames';

import './css/editor.scss';

export default function (props) {
	const [searchQuery, setSearchQuery] = useState('');
	const [selectedFolders, setSelectedFolders] = useState([]);
	const [icons, setIcons] = useState([]);
	const [currentPage, setCurrentPage] = useState(1);
	const [maxPages, setMaxPages] = useState(1);
	const iconsPerPage = 64;

	const folders = ['brands', 'light', 'regular', 'solid', 'thin'];

	const { attributes, setAttributes } = props;

	const { iconSize, iconColor, iconColorHover, selectedIcon, url, urlNewWindow, colorMode } = attributes;

	const units = [{ value: 'px', label: 'px', default: 32 }];

	const blockProps = useBlockProps();

	// Merging Gutenberg's blockProps with your custom class
	const mergedBlockProps = {
		...blockProps,
		className: classNames(blockProps.className, 'wawSvgIcon'),
	};

	const handleCheckboxChange = (folder) => {
		setSelectedFolders((prevFolders) => {
			const updatedFolders = prevFolders.includes(folder) ? prevFolders.filter((f) => f !== folder) : [...prevFolders, folder];
			return updatedFolders;
		});
	};

	// Define the debounce function outside the component
	const debounce = (func, wait) => {
		let timeout;
		return function executedFunction(...args) {
			const later = () => {
				clearTimeout(timeout);
				func(...args);
			};

			clearTimeout(timeout);
			timeout = setTimeout(later, wait);
		};
	};

	const fetchIcons = useCallback((query, page, folders) => {
		const folderQueryParam = folders.length > 0 ? `&folders=${encodeURIComponent(folders.join(','))}` : '';

		apiFetch({
			path: `/waw-blocks/v1/svg-icons/?search=${encodeURIComponent(query)}${folderQueryParam}&page=${page}`,
		}).then((data) => {
			setIcons(data.icons);
			// Set maxPages to 1 if no items are found, otherwise calculate normally
			const calculatedMaxPages = Math.ceil(data.total / iconsPerPage);
			setMaxPages(calculatedMaxPages > 0 ? calculatedMaxPages : 1);
		});
	}, []);

	const debouncedSearch = useCallback(
		debounce((query) => {
			setCurrentPage(1);
			fetchIcons(query, 1, selectedFolders);
		}, 1000),
		[fetchIcons]
	);

	useEffect(() => {
		if (searchQuery !== '') {
			debouncedSearch(searchQuery);
		}
	}, [searchQuery, debouncedSearch]);

	useEffect(() => {
		if (searchQuery !== '') {
			fetchIcons(searchQuery, currentPage, selectedFolders);
		}
	}, [selectedFolders, currentPage]);

	return (
		<Fragment>
			<InspectorControls>
				<Panel>
					<PanelBody
						title="Color Settings"
						icon={more}
						initialOpen={true}
					>
						<PanelRow>
							<SelectControl
								label="Color Mode"
								value={colorMode}
								options={[
									{ label: 'Auto (Detect currentColor)', value: 'auto' },
									{ label: 'CSS Color Property', value: 'color' },
									{ label: 'SVG Fill & Stroke', value: 'fill-stroke' },
								]}
								onChange={(value) => setAttributes({ colorMode: value })}
								help="Choose how colors are applied to the SVG icon"
							/>
						</PanelRow>
					</PanelBody>
					<PanelColorSettings
						title={'Icon Color'}
						colorSettings={[
							{
								value: iconColor,
								onChange: (value) => setAttributes({ iconColor: value }),
								label: 'Icon Color',
							},
							{
								value: iconColorHover,
								onChange: (value) => setAttributes({ iconColorHover: value }),
								label: 'Icon Color (Hover)',
							},
						]}
					></PanelColorSettings>
					<PanelBody
						title="Icon Settings"
						icon={more}
						initialOpen={true}
					>
						{attributes.selectedIcon && (
							<>
								<PanelRow>Current Icon</PanelRow>
								<PanelRow>
									<img
										className="wawCard__image-element"
										style={{ maxWidth: '24px' }}
										src={attributes.selectedIcon}
									/>
								</PanelRow>
								<PanelRow>
									<Button
										variant="secondary"
										size="small"
										className="block-library-cover__reset-button"
										onClick={() =>
											setAttributes({
												selectedIcon: undefined,
											})
										}
									>
										{__('Clear Media')}
									</Button>
								</PanelRow>
							</>
						)}
						<MediaUploadCheck>
							<MediaUpload
								value={selectedIcon}
								onSelect={(media) => {
									//console.log( 'selected: ' + media );
									let img_url = media.sizes.full.url;
									if (typeof media.sizes.medium != 'undefined') {
										img_url = media.sizes.medium.url;
									}
									setAttributes({ selectedIcon: img_url });
								}}
								type="image"
								render={(open) => {
									return (
										<Button
											onClick={open.open}
											className="components-button editor-post-publish-button editor-post-publish-button__button is-primary"
										>
											Select icon image
										</Button>
									);
								}}
							/>
						</MediaUploadCheck>
						<PanelRow>
							<div className="wawSvgIcon">
								<div
									className="wawSvgIcon__search"
									style={{ marginBottom: '20px' }}
								>
									<TextControl
										label="Search Icon"
										value={searchQuery}
										onChange={(value) => setSearchQuery(value)}
									/>
								</div>
								<div
									className="wawSvgIcon__categories"
									style={{ marginBottom: '20px' }}
								>
									{folders.map((folder) => (
										<CheckboxControl
											key={folder}
											label={folder.charAt(0).toUpperCase() + folder.slice(1)}
											checked={selectedFolders.includes(folder)}
											onChange={() => handleCheckboxChange(folder)}
										/>
									))}
								</div>
								{props.isSelected && (
									<div className="wawSvgIcon__wrapper">
										{searchQuery &&
											(icons && icons.length > 0
												? icons.map((icon) => (
														<a
															className={attributes.selectedIcon === icon.path ? 'wawSvgIcon__icon wawSvgIcon__icon--isSelected' : 'wawSvgIcon__icon'}
															onClick={() => setAttributes({ selectedIcon: icon.path })}
														>
															<img
																key={icon.name}
																src={icon.path}
																className="wawSvgIcon__icon-img"
															/>
															<p>{icon.name}</p>
														</a>
												  ))
												: 'Nothing found')}
									</div>
								)}
								{searchQuery && props.isSelected && (
									<div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '20px' }}>
										<Button
											variant="secondary"
											onClick={() => setCurrentPage(currentPage - 1)}
											disabled={currentPage === 1}
										>
											Previous
										</Button>
										<div className="wawSvgIcon__pagination">
											<div className="wawSvgIcon__pagination-current">{currentPage}</div>
											<div className="wawSvgIcon__pagination-of">of</div>
											<div className="wawSvgIcon__pagination-max">{maxPages}</div>
										</div>
										<Button
											variant="secondary"
											onClick={() => setCurrentPage(currentPage + 1)}
											disabled={currentPage === maxPages}
										>
											Next
										</Button>
									</div>
								)}
							</div>
						</PanelRow>

						<PanelRow>
							<UnitControl
								label="Width"
								onChange={(value) =>
									setAttributes({
										iconSize: value,
									})
								}
								value={iconSize}
								units={units}
							/>
						</PanelRow>
						<PanelRow>
							<p>Icon width. Height is auto.</p>
						</PanelRow>
						<PanelRow>
							<TextControl
								label="Link"
								value={url}
								onChange={(value) => setAttributes({ url: value })}
							/>
						</PanelRow>
						<PanelRow>
							<ToggleControl
								label="Open in a new window"
								checked={urlNewWindow}
								onChange={(value) => {
									setAttributes({ urlNewWindow: value });
								}}
							/>
						</PanelRow>
					</PanelBody>
				</Panel>
			</InspectorControls>
			<div {...mergedBlockProps}>
				{attributes.selectedIcon ? (
					<img
						className="wawCard__image-element"
						src={attributes.selectedIcon}
					/>
				) : (
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 512 512"
					>
						<path d="M152.014 120C125.504 120 104.014 141.492 104.014 168S125.504 216 152.014 216S200.014 194.508 200.014 168S178.523 120 152.014 120ZM448 32H64C28.654 32 0 60.652 0 96V416C0 451.346 28.654 480 64 480H448C483.348 480 512 451.346 512 416V96C512 60.652 483.348 32 448 32ZM464 409.303L327.229 223.379C323.838 218.768 318.129 216 312.014 216C305.9 216 300.191 218.768 296.801 223.379L190.166 368.338L153.074 322.236C149.633 317.957 144.141 315.428 138.301 315.428C132.459 315.428 126.967 317.957 123.525 322.236L48.018 416.084C48.018 416.055 48 416.029 48 416V96C48 87.178 55.178 80 64 80H448C456.822 80 464 87.178 464 96V409.303Z" />
					</svg>
				)}
			</div>
		</Fragment>
	);
}
