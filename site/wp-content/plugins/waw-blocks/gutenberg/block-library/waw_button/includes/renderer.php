<?php

namespace WAWBlocks\WawButton;

class Renderer
{
	public function render_callback($block_attributes, $content)
	{
		// Extract attributes with defaults
		$text = $block_attributes['text'] ?? 'Button';
		$url = $block_attributes['url'] ?? '';
		$urlNewWindow = $block_attributes['urlNewWindow'] ?? false;
		$iconId = $block_attributes['iconId'] ?? 0;
		$iconUrl = $block_attributes['iconUrl'] ?? '';
		$iconPosition = $block_attributes['iconPosition'] ?? 'left';
		$iconSize = $block_attributes['iconSize'] ?? '16px';
		$iconSpacing = $block_attributes['iconSpacing'] ?? '8px';
		$buttonStyle = $block_attributes['buttonStyle'] ?? 'primary';
		$buttonSize = $block_attributes['buttonSize'] ?? 'medium';
		$fullWidth = $block_attributes['fullWidth'] ?? false;
		$style = $block_attributes['style'] ?? [];

		// Generate unique block identifier
		$block_identifier = 'wawButton-' . wp_generate_uuid4();

		// Build CSS classes
		$classes = [
			'wawButton',
			'wawButton--' . $buttonStyle,
			'wawButton--' . $buttonSize
		];

		if ($fullWidth) {
			$classes[] = 'wawButton--fullWidth';
		}

		if ($iconUrl) {
			$classes[] = 'wawButton--hasIcon';
			$classes[] = 'wawButton--icon' . ucfirst($iconPosition);
		}

		$class_string = implode(' ', $classes);

		// Build icon HTML
		$icon_html = '';
		if ($iconUrl) {
			$icon_style = sprintf(
				'width: %s; height: %s; %s',
				esc_attr($iconSize),
				esc_attr($iconSize),
				$iconPosition === 'left'
					? 'margin-right: ' . esc_attr($iconSpacing) . ';'
					: 'margin-left: ' . esc_attr($iconSpacing) . ';'
			);

			// Check if it's an SVG file for inline rendering
			if ($this->is_svg_url($iconUrl)) {
				$svg_content = $this->get_svg_content($iconUrl);
				if ($svg_content) {
					$icon_html = sprintf(
						'<span class="wawButton__icon" style="%s">%s</span>',
						$icon_style,
						$svg_content
					);
				}
			}

			// Fallback to img tag if SVG inline failed or not SVG
			if (empty($icon_html)) {
				$icon_html = sprintf(
					'<img src="%s" alt="" class="wawButton__icon" style="%s" />',
					esc_url($iconUrl),
					$icon_style
				);
			}
		}

		// Build button content
		$button_content = '';
		if ($iconPosition === 'left' && $icon_html) {
			$button_content .= $icon_html;
		}
		$button_content .= '<span class="wawButton__text">' . esc_html($text) . '</span>';
		if ($iconPosition === 'right' && $icon_html) {
			$button_content .= $icon_html;
		}

		// Build link attributes
		$link_attributes = [
			'class' => 'wawButton__link',
			'id' => $block_identifier
		];

		if ($url) {
			$link_attributes['href'] = esc_url($url);
			if ($urlNewWindow) {
				$link_attributes['target'] = '_blank';
				$link_attributes['rel'] = 'noopener noreferrer';
			}
		} else {
			$link_attributes['role'] = 'button';
			$link_attributes['tabindex'] = '0';
		}

		// Convert attributes to string
		$attributes_string = '';
		foreach ($link_attributes as $attr => $value) {
			$attributes_string .= sprintf(' %s="%s"', $attr, esc_attr($value));
		}

		// Generate CSS for button styling
		$css = $this->generate_button_css($block_identifier, $block_attributes);

		// Build final HTML
		$html = sprintf(
			'<div class="%s">%s<%s%s>%s</%s></div>',
			esc_attr($class_string),
			$css,
			$url ? 'a' : 'button',
			$attributes_string,
			$button_content,
			$url ? 'a' : 'button'
		);

		return $html;
	}

	private function is_svg_url($url)
	{
		return pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION) === 'svg';
	}

	private function get_svg_content($icon_url)
	{
		// Convert URL to file path
		$upload_dir = wp_upload_dir();
		$file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $icon_url);

		if (file_exists($file_path)) {
			$svg_content = file_get_contents($file_path);

			// Basic SVG sanitization
			$allowed_svg_elements = [
				'svg' => [
					'class' => true,
					'width' => true,
					'height' => true,
					'viewBox' => true,
					'fill' => true,
					'stroke' => true,
					'xmlns' => true,
				],
				'path' => [
					'd' => true,
					'fill' => true,
					'stroke' => true,
					'stroke-width' => true,
					'stroke-linecap' => true,
					'stroke-linejoin' => true,
				],
				'g' => [
					'fill' => true,
					'stroke' => true,
					'transform' => true,
				],
				'circle' => [
					'cx' => true,
					'cy' => true,
					'r' => true,
					'fill' => true,
					'stroke' => true,
				],
				'rect' => [
					'x' => true,
					'y' => true,
					'width' => true,
					'height' => true,
					'fill' => true,
					'stroke' => true,
				],
			];

			return wp_kses($svg_content, $allowed_svg_elements);
		}

		return false;
	}

	private function generate_button_css($block_identifier, $attributes)
	{
		$css = "<style>";

		// Base button styles
		$css .= "#{$block_identifier} {";
		$css .= "display: inline-flex;";
		$css .= "align-items: center;";
		$css .= "justify-content: center;";
		$css .= "text-decoration: none;";
		$css .= "border: none;";
		$css .= "cursor: pointer;";
		$css .= "transition: all 0.3s ease;";
		$css .= "font-family: inherit;";
		$css .= "}";

		// Add custom colors if provided
		if (!empty($attributes['backgroundColor'])) {
			$css .= "#{$block_identifier} { background-color: " . esc_attr($attributes['backgroundColor']) . "; }";
		}

		if (!empty($attributes['textColor'])) {
			$css .= "#{$block_identifier} { color: " . esc_attr($attributes['textColor']) . "; }";
		}

		if (!empty($attributes['backgroundColorHover'])) {
			$css .= "#{$block_identifier}:hover { background-color: " . esc_attr($attributes['backgroundColorHover']) . "; }";
		}

		if (!empty($attributes['textColorHover'])) {
			$css .= "#{$block_identifier}:hover { color: " . esc_attr($attributes['textColorHover']) . "; }";
		}

		// Add WordPress core border styles if provided
		if (!empty($attributes['style']['border'])) {
			$border = $attributes['style']['border'];
			$border_css = '';

			if (!empty($border['width'])) {
				$border_css .= esc_attr($border['width']) . ' ';
			}

			if (!empty($border['style'])) {
				$border_css .= esc_attr($border['style']) . ' ';
			}

			if (!empty($border['color'])) {
				$border_css .= esc_attr($border['color']);
			}

			if (!empty($border_css)) {
				$css .= "#{$block_identifier} { border: " . trim($border_css) . "; }";
			}
		}

		// Add WordPress core border radius if provided
		if (!empty($attributes['style']['border']['radius'])) {
			$css .= "#{$block_identifier} { border-radius: " . esc_attr($attributes['style']['border']['radius']) . "; }";
		}

		$css .= "</style>";

		return $css;
	}
}
