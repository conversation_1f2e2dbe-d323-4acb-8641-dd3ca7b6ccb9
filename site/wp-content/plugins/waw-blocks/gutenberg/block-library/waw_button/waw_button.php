<?php

namespace WAWBlocks\WawButton;

// Include the renderer
require_once __DIR__ . '/includes/renderer.php';

class WawButton
{
	public function __construct()
	{
		add_action('init', [$this, 'register_block']);
	}

	public function register_block()
	{
		if (function_exists('is_gutenberg_active') && !$this->is_gutenberg_active()) {
			return;
		}

		if (is_admin()) {
			wp_enqueue_editor();
		}

		$renderer = new Renderer();

		register_block_type(__DIR__ . '/build', array(
			'render_callback' => [$renderer, 'render_callback']
		));
	}

	private function is_gutenberg_active()
	{
		// Check if <PERSON><PERSON><PERSON> is active
		if (function_exists('is_plugin_active')) {
			return is_plugin_active('gutenberg/gutenberg.php');
		}
		return false;
	}
}

new WawButton();
