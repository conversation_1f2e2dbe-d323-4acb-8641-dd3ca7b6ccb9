{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "waw-blocks/waw-button", "title": "WAW Button", "category": "design", "description": "A customizable button with icon support and advanced styling options.", "keywords": ["button", "link", "icon", "waw"], "textdomain": "waw-blocks", "attributes": {"text": {"type": "string", "default": "<PERSON><PERSON>"}, "url": {"type": "string", "default": ""}, "urlNewWindow": {"type": "boolean", "default": false}, "iconId": {"type": "number", "default": 0}, "iconUrl": {"type": "string", "default": ""}, "iconPosition": {"type": "string", "default": "left"}, "iconSize": {"type": "string", "default": "16px"}, "iconSpacing": {"type": "string", "default": "8px"}, "buttonStyle": {"type": "string", "default": "solid"}, "buttonSize": {"type": "string", "default": "medium"}, "fullWidth": {"type": "boolean", "default": false}, "backgroundColor": {"type": "string", "default": ""}, "textColor": {"type": "string", "default": ""}, "backgroundColorHover": {"type": "string", "default": ""}, "textColorHover": {"type": "string", "default": ""}, "style": {"type": "object", "default": {}}}, "supports": {"anchor": true, "html": false, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "fontFamily": true, "fontWeight": true}, "__experimentalBorder": {"color": true, "radius": true, "style": true, "width": true}}, "editorScript": "file:./index.js", "editorStyle": "file:./index.css", "style": "file:./style-index.css"}