/**
 * WAW Button Block - Editor Styles
 */

.waw-button {
	margin: 10px 0;

	.waw-button__link {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		text-decoration: none;
		border: none;
		cursor: pointer;
		transition: all 0.3s ease;
		font-family: inherit;
		font-size: inherit;
		line-height: 1.4;
		border-radius: 4px;
		
		&:focus {
			outline: 2px solid #007cba;
			outline-offset: 2px;
		}
	}

	.waw-button__text {
		display: inline-block;
	}

	.waw-button__icon {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;

		img, svg {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		svg {
			fill: currentColor;
		}
	}

	// Button Styles
	&.waw-button--primary .waw-button__link {
		background-color: #007cba;
		color: white;
		
		&:hover {
			background-color: #005a87;
		}
	}

	&.waw-button--secondary .waw-button__link {
		background-color: #f0f0f1;
		color: #1e1e1e;
		
		&:hover {
			background-color: #dcdcde;
		}
	}

	&.waw-button--outline .waw-button__link {
		background-color: transparent;
		color: #007cba;
		border: 2px solid #007cba;
		
		&:hover {
			background-color: #007cba;
			color: white;
		}
	}

	&.waw-button--ghost .waw-button__link {
		background-color: transparent;
		color: #007cba;
		
		&:hover {
			background-color: rgba(0, 124, 186, 0.1);
		}
	}

	// Button Sizes
	&.waw-button--small .waw-button__link {
		padding: 8px 16px;
		font-size: 14px;
	}

	&.waw-button--medium .waw-button__link {
		padding: 12px 24px;
		font-size: 16px;
	}

	&.waw-button--large .waw-button__link {
		padding: 16px 32px;
		font-size: 18px;
	}

	// Full Width
	&.waw-button--full-width {
		width: 100%;
		
		.waw-button__link {
			width: 100%;
		}
	}

	// Icon positioning
	&.waw-button--icon-left .waw-button__icon {
		order: -1;
	}

	&.waw-button--icon-right .waw-button__icon {
		order: 1;
	}
}

// URL Input styling in editor
.waw-button + .block-editor-url-input {
	margin-top: 10px;
}
