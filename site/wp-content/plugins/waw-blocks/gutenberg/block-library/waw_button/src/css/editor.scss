/**
 * WAW Button Block - Editor Styles
 * Uses WordPress core button classes for consistency
 */

.wp-block-button {
	// Custom icon and text styling for WAW buttons
	.waw-button-text {
		display: inline-block;
	}

	.waw-button-icon {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;

		img, svg {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		svg {
			fill: currentColor;
		}
	}

	// Ensure button link supports flexbox for icons
	.wp-block-button__link {
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}

	// Custom button styles (WordPress core handles primary by default)
	&.is-style-secondary .wp-block-button__link {
		background-color: #f0f0f1;
		color: #1e1e1e;

		&:hover {
			background-color: #dcdcde;
		}
	}

	&.is-style-ghost .wp-block-button__link {
		background-color: transparent;
		color: #007cba;

		&:hover {
			background-color: rgba(0, 124, 186, 0.1);
		}
	}

	// Custom button sizes
	&.wawButton--small .wp-block-button__link {
		padding: 8px 16px;
		font-size: 14px;
	}

	&.wawButton--large .wp-block-button__link {
		padding: 16px 32px;
		font-size: 18px;
	}

	// Full Width
	&.wawButton--fullWidth {
		width: 100%;

		.wp-block-button__link {
			width: 100%;
		}
	}

	// Icon positioning
	&.wawButton--iconLeft .waw-button-icon {
		order: -1;
	}

	&.wawButton--iconRight .waw-button-icon {
		order: 1;
	}
}

// URL Input styling in editor
.waw-button + .block-editor-url-input {
	margin-top: 10px;
}
