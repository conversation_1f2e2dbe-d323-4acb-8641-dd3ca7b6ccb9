/**
 * WAW Button Block - Editor Styles
 * Uses both WAW BEM classes and WordPress core button classes
 */

.wawButton {
	margin: 10px 0;

	.wawButton__link {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		text-decoration: none;
		border: none;
		cursor: pointer;
		transition: all 0.3s ease;
		font-family: inherit;
		font-size: inherit;
		line-height: 1.4;
		border-radius: 4px;

		&:focus {
			outline: 2px solid #007cba;
			outline-offset: 2px;
		}
	}

	.wawButton__text {
		display: inline-block;
	}

	.wawButton__icon {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;

		img, svg {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		svg {
			fill: currentColor;
		}
	}

	// Button Styles
	&.wawButton--primary .wawButton__link {
		background-color: #007cba;
		color: white;

		&:hover {
			background-color: #005a87;
		}
	}

	&.wawButton--secondary .wawButton__link {
		background-color: #f0f0f1;
		color: #1e1e1e;

		&:hover {
			background-color: #dcdcde;
		}
	}

	&.wawButton--outline .wawButton__link {
		background-color: transparent;
		color: #007cba;
		border: 2px solid #007cba;

		&:hover {
			background-color: #007cba;
			color: white;
		}
	}

	&.wawButton--ghost .wawButton__link {
		background-color: transparent;
		color: #007cba;

		&:hover {
			background-color: rgba(0, 124, 186, 0.1);
		}
	}

	// Button Sizes
	&.wawButton--small .wawButton__link {
		padding: 8px 16px;
		font-size: 14px;
	}

	&.wawButton--medium .wawButton__link {
		padding: 12px 24px;
		font-size: 16px;
	}

	&.wawButton--large .wawButton__link {
		padding: 16px 32px;
		font-size: 18px;
	}

	// Full Width
	&.wawButton--fullWidth {
		width: 100%;

		.wawButton__link {
			width: 100%;
		}
	}

	// Icon positioning
	&.wawButton--iconLeft .wawButton__icon {
		order: -1;
	}

	&.wawButton--iconRight .wawButton__icon {
		order: 1;
	}
}

// URL Input styling in editor
.waw-button + .block-editor-url-input {
	margin-top: 10px;
}
