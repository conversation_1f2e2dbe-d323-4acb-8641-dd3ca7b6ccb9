import { useBlockProps, InspectorControls, RichText, URLInput, MediaUpload, MediaUploadCheck, PanelColorSettings } from '@wordpress/block-editor';
import { Panel, PanelBody, PanelRow, SelectControl, Button, ToggleControl, BaseControl, __experimentalUnitControl as UnitControl } from '@wordpress/components';
import { __ } from '@wordpress/i18n';
import { more } from '@wordpress/icons';

export default function Edit({ attributes, setAttributes }) {
	const { text, url, urlNewWindow, iconId, iconUrl, iconPosition, iconSize, iconSpacing, buttonStyle, buttonSize, fullWidth, backgroundColor, textColor, backgroundColorHover, textColorHover, style } = attributes;

	const blockProps = useBlockProps({
		className: `wawButton wp-block-button wawButton--${buttonStyle} wawButton--${buttonSize} ${buttonStyle === 'outline' ? 'is-style-outline' : ''} ${fullWidth ? 'wawButton--fullWidth' : ''} ${iconUrl ? 'wawButton--hasIcon wawButton--icon' + iconPosition.charAt(0).toUpperCase() + iconPosition.slice(1) : ''}`,
	});

	const onSelectIcon = (media) => {
		setAttributes({
			iconId: media.id,
			iconUrl: media.url,
		});
	};

	const removeIcon = () => {
		setAttributes({
			iconId: 0,
			iconUrl: '',
		});
	};

	// Build icon element for preview
	const iconElement = iconUrl ? (
		<span
			className="wawButton__icon"
			style={{
				width: iconSize,
				height: iconSize,
				display: 'inline-block',
				marginRight: iconPosition === 'left' ? iconSpacing : '0',
				marginLeft: iconPosition === 'right' ? iconSpacing : '0',
			}}
		>
			<img
				src={iconUrl}
				alt=""
				style={{ width: '100%', height: '100%', objectFit: 'contain' }}
			/>
		</span>
	) : null;

	const units = [
		{ value: 'px', label: 'px' },
		{ value: 'em', label: 'em' },
		{ value: 'rem', label: 'rem' },
		{ value: '%', label: '%' },
	];

	return (
		<>
			<InspectorControls>
				<Panel>
					<PanelBody
						title="Button Settings"
						icon={more}
						initialOpen={true}
					>
						<PanelRow>
							<SelectControl
								label="Button Style"
								value={buttonStyle}
								options={[
									{ label: 'Solid', value: 'solid' },
									{ label: 'Outline', value: 'outline' },
								]}
								onChange={(value) => setAttributes({ buttonStyle: value })}
							/>
						</PanelRow>

						<PanelRow>
							<SelectControl
								label="Button Size"
								value={buttonSize}
								options={[
									{ label: 'Small', value: 'small' },
									{ label: 'Medium', value: 'medium' },
									{ label: 'Large', value: 'large' },
								]}
								onChange={(value) => setAttributes({ buttonSize: value })}
							/>
						</PanelRow>

						<PanelRow>
							<ToggleControl
								label="Full Width"
								checked={fullWidth}
								onChange={(value) => setAttributes({ fullWidth: value })}
							/>
						</PanelRow>

						<PanelRow>
							<BaseControl label="Button URL">
								<URLInput
									value={url}
									onChange={(value) => setAttributes({ url: value })}
									placeholder={__('Enter URL...')}
								/>
							</BaseControl>
						</PanelRow>

						<PanelRow>
							<ToggleControl
								label="Open in New Window"
								checked={urlNewWindow}
								onChange={(value) => setAttributes({ urlNewWindow: value })}
							/>
						</PanelRow>
					</PanelBody>

					<PanelBody
						title="Button Icon"
						icon={more}
						initialOpen={false}
					>
						<PanelRow>
							<BaseControl label="Icon">
								<MediaUploadCheck>
									<MediaUpload
										onSelect={onSelectIcon}
										allowedTypes={['image']}
										value={iconId}
										render={({ open }) => (
											<div>
												{iconUrl ? (
													<div style={{ marginBottom: '10px' }}>
														<img
															src={iconUrl}
															alt="Selected icon"
															style={{
																maxWidth: '50px',
																maxHeight: '50px',
																display: 'block',
																marginBottom: '8px',
															}}
														/>
														<Button
															onClick={open}
															variant="secondary"
															style={{ marginRight: '8px' }}
														>
															{__('Change Icon')}
														</Button>
														<Button
															onClick={removeIcon}
															variant="secondary"
															isDestructive
														>
															{__('Remove Icon')}
														</Button>
													</div>
												) : (
													<Button
														onClick={open}
														variant="secondary"
													>
														{__('Select Icon')}
													</Button>
												)}
											</div>
										)}
									/>
								</MediaUploadCheck>
							</BaseControl>
						</PanelRow>

						{iconUrl && (
							<>
								<PanelRow>
									<SelectControl
										label="Icon Position"
										value={iconPosition}
										options={[
											{ label: 'Left', value: 'left' },
											{ label: 'Right', value: 'right' },
										]}
										onChange={(value) => setAttributes({ iconPosition: value })}
									/>
								</PanelRow>

								<PanelRow>
									<UnitControl
										label="Icon Size"
										value={iconSize}
										units={units}
										onChange={(value) => setAttributes({ iconSize: value || '16px' })}
									/>
								</PanelRow>

								<PanelRow>
									<UnitControl
										label="Icon Spacing"
										value={iconSpacing}
										units={units}
										onChange={(value) => setAttributes({ iconSpacing: value || '8px' })}
									/>
								</PanelRow>
							</>
						)}
					</PanelBody>

					<PanelColorSettings
						title="Button Colors"
						colorSettings={[
							{
								value: backgroundColor,
								onChange: (value) => setAttributes({ backgroundColor: value }),
								label: 'Background Color',
							},
							{
								value: textColor,
								onChange: (value) => setAttributes({ textColor: value }),
								label: 'Text Color',
							},
							{
								value: backgroundColorHover,
								onChange: (value) => setAttributes({ backgroundColorHover: value }),
								label: 'Background Color (Hover)',
							},
							{
								value: textColorHover,
								onChange: (value) => setAttributes({ textColorHover: value }),
								label: 'Text Color (Hover)',
							},
						]}
					/>


				</Panel>
			</InspectorControls>

			<div {...blockProps}>
				<a
					className="wawButton__link wp-block-button__link wp-element-button"
					href="#"
					onClick={(e) => e.preventDefault()}
					style={{
						backgroundColor: backgroundColor || undefined,
						color: textColor || undefined,
						display: 'inline-flex',
						alignItems: 'center',
						padding: '12px 24px',
						borderRadius: style?.border?.radius || '4px',
						textDecoration: 'none',
						border: style?.border ?
							`${style.border.width || '1px'} ${style.border.style || 'solid'} ${style.border.color || '#000'}` :
							'none',
						cursor: 'pointer',
					}}
				>
					{iconPosition === 'left' && iconElement}
					<RichText
						tagName="span"
						className="wawButton__text"
						value={text}
						onChange={(value) => setAttributes({ text: value })}
						placeholder={__('Button text...')}
						allowedFormats={[]}
					/>
					{iconPosition === 'right' && iconElement}
				</a>
			</div>
		</>
	);
}
