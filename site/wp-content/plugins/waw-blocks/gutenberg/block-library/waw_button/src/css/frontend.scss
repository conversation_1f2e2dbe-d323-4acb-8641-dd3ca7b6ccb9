/**
 * WAW Button Block - Frontend Styles
 * Uses WordPress core button classes for consistency
 */

.wp-block-button {
	// Custom icon and text styling for WAW buttons
	.waw-button-text {
		display: inline-block;
	}

	.waw-button-icon {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;

		img, svg {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}

		svg {
			fill: currentColor;
			stroke: currentColor;
		}
	}

	// Ensure button link supports flexbox for icons
	.wp-block-button__link {
		display: inline-flex;
		align-items: center;
		justify-content: center;

		&:active {
			transform: translateY(1px);
		}
	}

	// Custom button styles (WordPress core handles primary and outline by default)
	&.is-style-secondary .wp-block-button__link {
		background-color: #f0f0f1;
		color: #1e1e1e;

		&:hover {
			background-color: #dcdcde;
		}
	}

	&.is-style-ghost .wp-block-button__link {
		background-color: transparent;
		color: #007cba;

		&:hover {
			background-color: rgba(0, 124, 186, 0.1);
		}
	}

	// Custom button sizes
	&.wawButton--small .wp-block-button__link {
		padding: 8px 16px;
		font-size: 14px;
	}

	&.wawButton--large .wp-block-button__link {
		padding: 16px 32px;
		font-size: 18px;
	}

	// Full Width
	&.wawButton--fullWidth {
		width: 100%;

		.wp-block-button__link {
			width: 100%;
		}
	}

	// Icon positioning
	&.wawButton--iconLeft .waw-button-icon {
		order: -1;
	}

	&.wawButton--iconRight .waw-button-icon {
		order: 1;
	}

	// Responsive adjustments
	@media (max-width: 768px) {
		&.wawButton--large .wp-block-button__link {
			padding: 14px 28px;
			font-size: 16px;
		}

		.waw-button-icon {
			// Slightly smaller icons on mobile
			transform: scale(0.9);
		}
	}

	// High contrast mode support
	@media (prefers-contrast: high) {
		.wp-block-button__link {
			border: 2px solid currentColor;
		}
	}

	// Reduced motion support
	@media (prefers-reduced-motion: reduce) {
		.wp-block-button__link {
			transition: none;

			&:active {
				transform: none;
			}
		}
	}
}
