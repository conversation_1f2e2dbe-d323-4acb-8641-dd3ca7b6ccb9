(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,l=window.React,n=window.wp.primitives,o=(0,l.createElement)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,l.createElement)(n.Path,{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"})),a=window.wp.blockEditor,i=window.wp.components,r=(0,l.createElement)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,l.createElement)(n.<PERSON>,{d:"M4 9v1.5h16V9H4zm12 5.5h4V13h-4v1.5zm-6 0h4V13h-4v1.5zm-6 0h4V13H4v1.5z"})),c=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"waw-blocks/waw-button","title":"WAW Button","category":"design","description":"A customizable button with icon support and advanced styling options.","keywords":["button","link","icon","waw"],"textdomain":"waw-blocks","attributes":{"text":{"type":"string","default":"Button"},"url":{"type":"string","default":""},"urlNewWindow":{"type":"boolean","default":false},"iconId":{"type":"number","default":0},"iconUrl":{"type":"string","default":""},"iconPosition":{"type":"string","default":"left"},"iconSize":{"type":"string","default":"16px"},"iconSpacing":{"type":"string","default":"8px"},"buttonStyle":{"type":"string","default":"primary"},"buttonSize":{"type":"string","default":"medium"},"fullWidth":{"type":"boolean","default":false},"backgroundColor":{"type":"string","default":""},"textColor":{"type":"string","default":""},"backgroundColorHover":{"type":"string","default":""},"textColorHover":{"type":"string","default":""}},"supports":{"anchor":true,"html":false,"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"fontFamily":true,"fontWeight":true}},"editorScript":"file:./index.js","editorStyle":"file:./index.css","style":"file:./style-index.css"}');(0,e.registerBlockType)(c.name,{...c,icon:{src:o,foreground:"#2271b1"},edit:function({attributes:e,setAttributes:n,isSelected:o}){const{text:c,url:u,urlNewWindow:s,iconId:d,iconUrl:m,iconPosition:g,iconSize:p,iconSpacing:w,buttonStyle:b,buttonSize:h,fullWidth:v,backgroundColor:y,textColor:C,backgroundColorHover:E,textColorHover:x}=e,f=(0,a.useBlockProps)({className:`waw-button waw-button--${b} waw-button--${h} ${v?"waw-button--full-width":""} ${m?"waw-button--has-icon waw-button--icon-"+g:""}`}),S=()=>{n({iconId:0,iconUrl:""})},k=m?(0,l.createElement)("span",{className:"waw-button__icon",style:{width:p,height:p,display:"inline-block",marginRight:"left"===g?w:"0",marginLeft:"right"===g?w:"0"}},(0,l.createElement)("img",{src:m,alt:"",style:{width:"100%",height:"100%",objectFit:"contain"}})):null,B=[{value:"px",label:"px"},{value:"em",label:"em"},{value:"rem",label:"rem"},{value:"%",label:"%"}];return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(a.InspectorControls,null,(0,l.createElement)(i.Panel,null,(0,l.createElement)(i.PanelBody,{title:"Button Settings",icon:r,initialOpen:!0},(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.SelectControl,{label:"Button Style",value:b,options:[{label:"Primary",value:"primary"},{label:"Secondary",value:"secondary"},{label:"Outline",value:"outline"},{label:"Ghost",value:"ghost"}],onChange:e=>n({buttonStyle:e})})),(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.SelectControl,{label:"Button Size",value:h,options:[{label:"Small",value:"small"},{label:"Medium",value:"medium"},{label:"Large",value:"large"}],onChange:e=>n({buttonSize:e})})),(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.ToggleControl,{label:"Full Width",checked:v,onChange:e=>n({fullWidth:e})})),(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.ToggleControl,{label:"Open in New Window",checked:s,onChange:e=>n({urlNewWindow:e})}))),(0,l.createElement)(i.PanelBody,{title:"Button Icon",icon:r,initialOpen:!1},(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.BaseControl,{label:"Icon"},(0,l.createElement)(a.MediaUploadCheck,null,(0,l.createElement)(a.MediaUpload,{onSelect:e=>{n({iconId:e.id,iconUrl:e.url})},allowedTypes:["image"],value:d,render:({open:e})=>(0,l.createElement)("div",null,m?(0,l.createElement)("div",{style:{marginBottom:"10px"}},(0,l.createElement)("img",{src:m,alt:"Selected icon",style:{maxWidth:"50px",maxHeight:"50px",display:"block",marginBottom:"8px"}}),(0,l.createElement)(i.Button,{onClick:e,variant:"secondary",style:{marginRight:"8px"}},(0,t.__)("Change Icon")),(0,l.createElement)(i.Button,{onClick:S,variant:"secondary",isDestructive:!0},(0,t.__)("Remove Icon"))):(0,l.createElement)(i.Button,{onClick:e,variant:"secondary"},(0,t.__)("Select Icon")))})))),m&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.SelectControl,{label:"Icon Position",value:g,options:[{label:"Left",value:"left"},{label:"Right",value:"right"}],onChange:e=>n({iconPosition:e})})),(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.__experimentalUnitControl,{label:"Icon Size",value:p,units:B,onChange:e=>n({iconSize:e||"16px"})})),(0,l.createElement)(i.PanelRow,null,(0,l.createElement)(i.__experimentalUnitControl,{label:"Icon Spacing",value:w,units:B,onChange:e=>n({iconSpacing:e||"8px"})})))),(0,l.createElement)(a.PanelColorSettings,{title:"Button Colors",colorSettings:[{value:y,onChange:e=>n({backgroundColor:e}),label:"Background Color"},{value:C,onChange:e=>n({textColor:e}),label:"Text Color"},{value:E,onChange:e=>n({backgroundColorHover:e}),label:"Background Color (Hover)"},{value:x,onChange:e=>n({textColorHover:e}),label:"Text Color (Hover)"}]}))),(0,l.createElement)("div",{...f},(0,l.createElement)("div",{className:"waw-button__link",style:{backgroundColor:y||void 0,color:C||void 0,display:"inline-flex",alignItems:"center",padding:"12px 24px",borderRadius:"4px",textDecoration:"none",border:"none",cursor:"pointer"}},"left"===g&&k,(0,l.createElement)(a.RichText,{tagName:"span",className:"waw-button__text",value:c,onChange:e=>n({text:e}),placeholder:(0,t.__)("Button text..."),allowedFormats:[]}),"right"===g&&k),o&&(0,l.createElement)(a.URLInput,{value:u,onChange:e=>n({url:e}),placeholder:(0,t.__)("Enter URL...")})))}})})();