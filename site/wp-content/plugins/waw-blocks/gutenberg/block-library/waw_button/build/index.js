(()=>{"use strict";const e=window.wp.blocks,t=window.wp.i18n,l=window.React,n=window.wp.primitives,o=(0,l.createElement)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,l.createElement)(n.Path,{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"})),a=window.wp.blockEditor,r=window.wp.components,i=(0,l.createElement)(n.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,l.createElement)(n.<PERSON>,{d:"M4 9v1.5h16V9H4zm12 5.5h4V13h-4v1.5zm-6 0h4V13h-4v1.5zm-6 0h4V13H4v1.5z"})),c=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"waw-blocks/waw-button","title":"WAW Button","category":"design","description":"A customizable button with icon support and advanced styling options.","keywords":["button","link","icon","waw"],"textdomain":"waw-blocks","attributes":{"text":{"type":"string","default":"Button"},"url":{"type":"string","default":""},"urlNewWindow":{"type":"boolean","default":false},"iconId":{"type":"number","default":0},"iconUrl":{"type":"string","default":""},"iconPosition":{"type":"string","default":"left"},"iconSize":{"type":"string","default":"16px"},"iconSpacing":{"type":"string","default":"8px"},"buttonStyle":{"type":"string","default":"primary"},"buttonSize":{"type":"string","default":"medium"},"fullWidth":{"type":"boolean","default":false},"backgroundColor":{"type":"string","default":""},"textColor":{"type":"string","default":""},"backgroundColorHover":{"type":"string","default":""},"textColorHover":{"type":"string","default":""},"style":{"type":"object","default":{}}},"supports":{"anchor":true,"html":false,"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"fontFamily":true,"fontWeight":true},"__experimentalBorder":{"color":true,"radius":true,"style":true,"width":true}},"editorScript":"file:./index.js","editorStyle":"file:./index.css","style":"file:./style-index.css"}');(0,e.registerBlockType)(c.name,{...c,icon:{src:o,foreground:"#2271b1"},edit:function({attributes:e,setAttributes:n}){const{text:o,url:c,urlNewWindow:u,iconId:s,iconUrl:d,iconPosition:m,iconSize:p,iconSpacing:g,buttonStyle:w,buttonSize:b,fullWidth:h,backgroundColor:y,textColor:v,backgroundColorHover:C,textColorHover:E,style:x}=e,f=(0,a.useBlockProps)({className:`wawButton wp-block-button wawButton--${w} wawButton--${b} ${"primary"!==w?"is-style-"+w:""} ${h?"wawButton--fullWidth":""} ${d?"wawButton--hasIcon wawButton--icon"+m.charAt(0).toUpperCase()+m.slice(1):""}`}),B=()=>{n({iconId:0,iconUrl:""})},S=d?(0,l.createElement)("span",{className:"wawButton__icon",style:{width:p,height:p,display:"inline-block",marginRight:"left"===m?g:"0",marginLeft:"right"===m?g:"0"}},(0,l.createElement)("img",{src:d,alt:"",style:{width:"100%",height:"100%",objectFit:"contain"}})):null,k=[{value:"px",label:"px"},{value:"em",label:"em"},{value:"rem",label:"rem"},{value:"%",label:"%"}];return(0,l.createElement)(l.Fragment,null,(0,l.createElement)(a.InspectorControls,null,(0,l.createElement)(r.Panel,null,(0,l.createElement)(r.PanelBody,{title:"Button Settings",icon:i,initialOpen:!0},(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.SelectControl,{label:"Button Style",value:w,options:[{label:"Primary",value:"primary"},{label:"Secondary",value:"secondary"},{label:"Outline",value:"outline"},{label:"Ghost",value:"ghost"}],onChange:e=>n({buttonStyle:e})})),(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.SelectControl,{label:"Button Size",value:b,options:[{label:"Small",value:"small"},{label:"Medium",value:"medium"},{label:"Large",value:"large"}],onChange:e=>n({buttonSize:e})})),(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.ToggleControl,{label:"Full Width",checked:h,onChange:e=>n({fullWidth:e})})),(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.BaseControl,{label:"Button URL"},(0,l.createElement)(a.URLInput,{value:c,onChange:e=>n({url:e}),placeholder:(0,t.__)("Enter URL...")}))),(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.ToggleControl,{label:"Open in New Window",checked:u,onChange:e=>n({urlNewWindow:e})}))),(0,l.createElement)(r.PanelBody,{title:"Button Icon",icon:i,initialOpen:!1},(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.BaseControl,{label:"Icon"},(0,l.createElement)(a.MediaUploadCheck,null,(0,l.createElement)(a.MediaUpload,{onSelect:e=>{n({iconId:e.id,iconUrl:e.url})},allowedTypes:["image"],value:s,render:({open:e})=>(0,l.createElement)("div",null,d?(0,l.createElement)("div",{style:{marginBottom:"10px"}},(0,l.createElement)("img",{src:d,alt:"Selected icon",style:{maxWidth:"50px",maxHeight:"50px",display:"block",marginBottom:"8px"}}),(0,l.createElement)(r.Button,{onClick:e,variant:"secondary",style:{marginRight:"8px"}},(0,t.__)("Change Icon")),(0,l.createElement)(r.Button,{onClick:B,variant:"secondary",isDestructive:!0},(0,t.__)("Remove Icon"))):(0,l.createElement)(r.Button,{onClick:e,variant:"secondary"},(0,t.__)("Select Icon")))})))),d&&(0,l.createElement)(l.Fragment,null,(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.SelectControl,{label:"Icon Position",value:m,options:[{label:"Left",value:"left"},{label:"Right",value:"right"}],onChange:e=>n({iconPosition:e})})),(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.__experimentalUnitControl,{label:"Icon Size",value:p,units:k,onChange:e=>n({iconSize:e||"16px"})})),(0,l.createElement)(r.PanelRow,null,(0,l.createElement)(r.__experimentalUnitControl,{label:"Icon Spacing",value:g,units:k,onChange:e=>n({iconSpacing:e||"8px"})})))),(0,l.createElement)(a.PanelColorSettings,{title:"Button Colors",colorSettings:[{value:y,onChange:e=>n({backgroundColor:e}),label:"Background Color"},{value:v,onChange:e=>n({textColor:e}),label:"Text Color"},{value:C,onChange:e=>n({backgroundColorHover:e}),label:"Background Color (Hover)"},{value:E,onChange:e=>n({textColorHover:e}),label:"Text Color (Hover)"}]}))),(0,l.createElement)("div",{...f},(0,l.createElement)("div",{className:"wawButton__link wp-block-button__link wp-element-button",style:{backgroundColor:y||void 0,color:v||void 0,display:"inline-flex",alignItems:"center",padding:"12px 24px",borderRadius:x?.border?.radius||"4px",textDecoration:"none",border:x?.border?`${x.border.width||"1px"} ${x.border.style||"solid"} ${x.border.color||"#000"}`:"none",cursor:"pointer"}},"left"===m&&S,(0,l.createElement)(a.RichText,{tagName:"span",className:"wawButton__text",value:o,onChange:e=>n({text:e}),placeholder:(0,t.__)("Button text..."),allowedFormats:[]}),"right"===m&&S)))}})})();