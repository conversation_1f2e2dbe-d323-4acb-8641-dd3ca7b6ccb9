<?php

namespace WAWBlocks\API;

class Get_Svg
{
	public function __construct()
	{
		// In your WordPress theme's functions.php or a custom plugin

		// Register custom REST API endpoint
		add_action('rest_api_init', function () {
			register_rest_route('waw-blocks/v1', '/get-svg/(?P<id>\d+)', array(
				'methods' => 'GET',
				'callback' => [$this, 'waw_blocks_get_svg_by_id'],
				'permission_callback' => '__return_true'
			));
		});
	}

	// Custom endpoint callback function
	function waw_blocks_get_svg_by_id($data)
	{
		// Get the ID from the request parameters
		$id = $data['id'];

		// Get the path to the SVG file using the ID
		$file_path = get_attached_file($id);

		if (empty($file_path)) {

			// Return proper WordPress REST API error
			return new \WP_Error(
				'not_found',
				'The requested SVG file was not found.',
				array('status' => 404)
			);
		}

		// Read the contents of the SVG file
		$svg_content = file_get_contents($file_path);

		// Define an array of allowed SVG elements and their attributes
		$allowed_svg_elements = array(
			'svg'   => array(
				'class'           => true,
				'aria-hidden'     => true,
				'aria-labelledby' => true,
				'role'            => true,
				'xmlns'           => true,
				'xmlns:xlink'     => true,
				'width'           => true,
				'height'          => true,
				'viewBox'         => true,
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'id'              => true,
			),
			'g'     => array(
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'clip-path'       => true,
				'id'              => true,
				'class'           => true,
				'transform'       => true,
			),
			'title' => array('title' => true),
			'path'  => array(
				'd'               => true,
				'fill'            => true,
				'fill-rule'       => true,
				'clip-rule'       => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'stroke-linecap'  => true,
				'stroke-linejoin' => true,
				'stroke-dasharray' => true,
				'stroke-dashoffset' => true,
				'stroke-opacity'  => true,
				'fill-opacity'    => true,
				'opacity'         => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'rect'  => array(
				'x'               => true,
				'y'               => true,
				'width'           => true,
				'height'          => true,
				'rx'              => true,
				'ry'              => true,
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'stroke-linecap'  => true,
				'stroke-linejoin' => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'circle' => array(
				'cx'              => true,
				'cy'              => true,
				'r'               => true,
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'stroke-linecap'  => true,
				'stroke-linejoin' => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'ellipse' => array(
				'cx'              => true,
				'cy'              => true,
				'rx'              => true,
				'ry'              => true,
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'line'  => array(
				'x1'              => true,
				'y1'              => true,
				'x2'              => true,
				'y2'              => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'stroke-linecap'  => true,
				'stroke-linejoin' => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'polyline' => array(
				'points'          => true,
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'stroke-linecap'  => true,
				'stroke-linejoin' => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'polygon' => array(
				'points'          => true,
				'fill'            => true,
				'stroke'          => true,
				'stroke-width'    => true,
				'stroke-linecap'  => true,
				'stroke-linejoin' => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'defs' => array(
				'id'              => true,
			),
			'clipPath' => array(
				'id'              => true,
				'clipPathUnits'   => true,
			),
			'pattern' => array(
				'id'                 => true,
				'patternUnits'       => true,
				'patternContentUnits' => true,
				'width'              => true,
				'height'             => true,
				'x'                  => true,
				'y'                  => true,
				'viewBox'            => true,
				'patternTransform'   => true,
			),
			'use' => array(
				'xlink:href' => true,
				'href'       => true,
				'transform'  => true,
				'x'          => true,
				'y'          => true,
				'width'      => true,
				'height'     => true,
				'id'         => true,
				'class'      => true,
			),
			'image' => array(
				'id'         => true,
				'width'      => true,
				'height'     => true,
				'x'          => true,
				'y'          => true,
				'xlink:href' => true,
				'href'       => true,
				'transform'  => true,
				'class'      => true,
			),
			'text' => array(
				'x'               => true,
				'y'               => true,
				'dx'              => true,
				'dy'              => true,
				'font-family'     => true,
				'font-size'       => true,
				'font-weight'     => true,
				'text-anchor'     => true,
				'fill'            => true,
				'stroke'          => true,
				'transform'       => true,
				'id'              => true,
				'class'           => true,
			),
			'tspan' => array(
				'x'               => true,
				'y'               => true,
				'dx'              => true,
				'dy'              => true,
				'font-family'     => true,
				'font-size'       => true,
				'font-weight'     => true,
				'text-anchor'     => true,
				'fill'            => true,
				'stroke'          => true,
				'id'              => true,
				'class'           => true,
			),
		);

		// Sanitize the SVG content using wp_kses
		$sanitized_svg_content = wp_kses($svg_content, $allowed_svg_elements);
		// Set the Content-Type header to indicate SVG content
		header('Content-Type: image/svg+xml');
		echo $sanitized_svg_content;
		exit;
	}
}
