<?php

/**
 * Functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Lesson One
 * @since 1.0.0
 */
require_once get_template_directory() . '/app/bootstrap.php';

/**
 * Enqueue the js file.
 * 
 * @since 1.0.0
 */
function waw_theme_scripts()
{
	wp_enqueue_script(
		'waw-theme-styles',
		get_template_directory_uri() . '/assets/js/bundle.js',
		['jquery'],
		wp_get_theme()->get('Version')
	);
}
add_action('wp_enqueue_scripts', 'waw_theme_scripts');

/**
 * Enqueue the style.css file.
 * 
 * @since 1.0.0
 */
function waw_theme_styles()
{
	wp_enqueue_style(
		'waw-theme-styles',
		get_template_directory_uri() . '/assets/css/main.css',
		[],
		wp_get_theme()->get('Version')
	);
}
add_action('wp_enqueue_scripts', 'waw_theme_styles');

/**
 * Add theme support for wide alignment
 *
 * @since 1.0.0
 */
function waw_theme_setup()
{
	// Add support for wide alignment
	add_theme_support('align-wide');
}
add_action('after_setup_theme', 'waw_theme_setup');


// Allow SVG
add_filter('wp_check_filetype_and_ext', function ($data, $file, $filename, $mimes) {

	global $wp_version;
	if ($wp_version !== '4.7.1') {
		return $data;
	}

	$filetype = wp_check_filetype($filename, $mimes);

	return [
		'ext'             => $filetype['ext'],
		'type'            => $filetype['type'],
		'proper_filename' => $data['proper_filename']
	];
}, 10, 4);

function cc_mime_types($mimes)
{
	$mimes['svg'] = 'image/svg+xml';
	return $mimes;
}
add_filter('upload_mimes', 'cc_mime_types');

// Enhanced SVG upload handling
function waw_sanitize_svg_upload($file)
{
	// Only process SVG files
	if (!isset($file['type']) || $file['type'] !== 'image/svg+xml') {
		return $file;
	}

	// Read the SVG content
	$svg_content = file_get_contents($file['tmp_name']);

	if (empty($svg_content)) {
		return $file;
	}

	// Define comprehensive allowed SVG elements and attributes
	$allowed_svg_elements = array(
		'svg'   => array(
			'class'           => true,
			'aria-hidden'     => true,
			'aria-labelledby' => true,
			'role'            => true,
			'xmlns'           => true,
			'xmlns:xlink'     => true,
			'width'           => true,
			'height'          => true,
			'viewBox'         => true,
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'id'              => true,
		),
		'g'     => array(
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'clip-path'       => true,
			'id'              => true,
			'class'           => true,
			'transform'       => true,
		),
		'title' => array('title' => true),
		'path'  => array(
			'd'               => true,
			'fill'            => true,
			'fill-rule'       => true,
			'clip-rule'       => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'stroke-linecap'  => true,
			'stroke-linejoin' => true,
			'stroke-dasharray' => true,
			'stroke-dashoffset' => true,
			'stroke-opacity'  => true,
			'fill-opacity'    => true,
			'opacity'         => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'rect'  => array(
			'x'               => true,
			'y'               => true,
			'width'           => true,
			'height'          => true,
			'rx'              => true,
			'ry'              => true,
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'stroke-linecap'  => true,
			'stroke-linejoin' => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'circle' => array(
			'cx'              => true,
			'cy'              => true,
			'r'               => true,
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'stroke-linecap'  => true,
			'stroke-linejoin' => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'ellipse' => array(
			'cx'              => true,
			'cy'              => true,
			'rx'              => true,
			'ry'              => true,
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'line'  => array(
			'x1'              => true,
			'y1'              => true,
			'x2'              => true,
			'y2'              => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'stroke-linecap'  => true,
			'stroke-linejoin' => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'polyline' => array(
			'points'          => true,
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'stroke-linecap'  => true,
			'stroke-linejoin' => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'polygon' => array(
			'points'          => true,
			'fill'            => true,
			'stroke'          => true,
			'stroke-width'    => true,
			'stroke-linecap'  => true,
			'stroke-linejoin' => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'defs' => array(
			'id'              => true,
		),
		'clipPath' => array(
			'id'              => true,
			'clipPathUnits'   => true,
		),
		'pattern' => array(
			'id'                 => true,
			'patternUnits'       => true,
			'patternContentUnits' => true,
			'width'              => true,
			'height'             => true,
			'x'                  => true,
			'y'                  => true,
			'viewBox'            => true,
			'patternTransform'   => true,
		),
		'use' => array(
			'xlink:href' => true,
			'href'       => true,
			'transform'  => true,
			'x'          => true,
			'y'          => true,
			'width'      => true,
			'height'     => true,
			'id'         => true,
			'class'      => true,
		),
		'image' => array(
			'id'         => true,
			'width'      => true,
			'height'     => true,
			'x'          => true,
			'y'          => true,
			'xlink:href' => true,
			'href'       => true,
			'transform'  => true,
			'class'      => true,
		),
		'text' => array(
			'x'               => true,
			'y'               => true,
			'dx'              => true,
			'dy'              => true,
			'font-family'     => true,
			'font-size'       => true,
			'font-weight'     => true,
			'text-anchor'     => true,
			'fill'            => true,
			'stroke'          => true,
			'transform'       => true,
			'id'              => true,
			'class'           => true,
		),
		'tspan' => array(
			'x'               => true,
			'y'               => true,
			'dx'              => true,
			'dy'              => true,
			'font-family'     => true,
			'font-size'       => true,
			'font-weight'     => true,
			'text-anchor'     => true,
			'fill'            => true,
			'stroke'          => true,
			'id'              => true,
			'class'           => true,
		),
	);

	// Sanitize the SVG content
	$sanitized_svg_content = wp_kses($svg_content, $allowed_svg_elements);

	// Write the sanitized content back to the temp file
	file_put_contents($file['tmp_name'], $sanitized_svg_content);

	return $file;
}

// Hook into the upload process
add_filter('wp_handle_upload_prefilter', 'waw_sanitize_svg_upload');


// Unregister default patterns
add_action('init', 'wpdocs_remove_core_patterns');

function wpdocs_remove_core_patterns()
{
	add_theme_support('appearance-tools');

	$core_block_patterns = array(
		'query-standard-posts',
		'query-medium-posts',
		'query-small-posts',
		'query-grid-posts',
		'query-large-title-posts',
		'query-offset-posts',
	);

	foreach ($core_block_patterns as $core_block_pattern) {
		unregister_block_pattern("core/{$core_block_pattern}");
	}
	remove_theme_support('core-block-patterns');
}

function add_reusable_blocks_admin_menu()
{
	add_menu_page(
		__('Reusable Blocks', 'textdomain'),
		__('Reusable Blocks', 'textdomain'),
		'edit_posts',
		'edit.php?post_type=wp_block',
		'',
		'dashicons-editor-table', // or any other dashicon
		22
	);
}
add_action('admin_menu', 'add_reusable_blocks_admin_menu');

function get_default_images_url()
{
	return get_stylesheet_directory_uri() . '/assets/images/default';
}

// To set the default sort order for the 'navigation' custom post type in the WordPress admin area based on the 'order' field (which corresponds to the menu_order attribute in the database), you will need to hook into the pre_get_posts action and modify the query for the 'navigation' post type in the admin area. This ensures that whenever you view the list of 'navigation' items in the WordPress admin, they are automatically sorted according to their menu order.

function set_default_sort_order_for_navigation_admin_list($query)
{
	if (!is_admin()) return;

	$post_type = $query->get('post_type');

	// Check if we're viewing the 'navigation' post type
	if ('nav' == $post_type) {
		// Check if 'orderby' is not already set
		if (!$query->get('orderby')) {
			$query->set('orderby', 'menu_order');
			$query->set('order', 'ASC');
		}
	}
}
add_action('pre_get_posts', 'set_default_sort_order_for_navigation_admin_list');

// Userd for search
// Dealing with custom post types
// if custom post type submitted
// then search for just that one custom post type
add_action('pre_get_posts', function ($query) {
	if ($query->is_search && !is_admin() && $query->is_main_query()) {
		// Define the whitelist of post types
		$whitelisted_post_types = array('course'); // Add other post types to the array as needed

		// Capture the post_type query variable if it exists
		$post_type = $query->get('post_type');

		// If the post_type query variable is set and is one of the whitelisted post types, use it
		if (!empty($post_type) && in_array($post_type, $whitelisted_post_types, true)) {
			$query->set('post_type', $post_type);
		} else {
			// Default behavior if no valid post_type is provided in the query
			// This can be adjusted as needed, for example, to only search 'post' or include multiple default post types
			$query->set('post_type', array('post', 'page'));
		}
	}
});
// START - TAXONOMY IMAGE UPLOADER
// ---------------------------------------------------------------------------
// Taxonomy Image Uploader
// Add meta box to taxonomy term edit screen
add_action('course_category_edit_form_fields', 'add_taxonomy_image_uploader');
function add_taxonomy_image_uploader($term)
{
?>
	<table class="form-table" role="presentation">
		<tbody>
			<tr class="form-field form-required term-name-wrap">
				<th scope="row"><label for="name">Category Image</label></th>
				<td>
					<input type="hidden" name="taxonomy_image" id="taxonomy_image" class="custom-media-url" value="<?php echo esc_attr(get_term_meta($term->term_id, 'taxonomy_image', true)); ?>">
					<img src="<?php echo esc_url(get_term_meta($term->term_id, 'taxonomy_image', true)); ?>" style="max-width: 100px;" />
					<input type="button" class="button button-secondary custom-media-button" id="taxonomy_image_button" value="<?php _e('Upload/Add Image', 'text-domain'); ?>" />
					<input type="button" class="button button-secondary custom-media-remove" value="<?php _e('Remove Image', 'text-domain'); ?>" />
					<p class="description" id="name-description">Use this field to upload an image for the category. You can upload an image that best represents the category, such as a logo, icon, or featured image. </p>
				</td>
			</tr>
		</tbody>
	</table>
<?php
}

// Enqueue scripts and styles for media uploader
add_action('admin_enqueue_scripts', 'taxonomy_image_uploader_scripts');
function taxonomy_image_uploader_scripts($hook)
{
	if (!is_user_logged_in() || !current_user_can('manage_categories')) {
		return;
	}
	if ($hook == 'edit-tags.php' || $hook == 'term.php') {
		wp_enqueue_media();
		wp_enqueue_script('custom-taxonomy-image-uploader', get_template_directory_uri() . '/assets/js/custom-taxonomy-image-uploader.js', array('jquery'), null, true);
	}
}

// Save custom field data
// Save custom field data
add_action('edited_course_category', 'save_taxonomy_custom_fields');
function save_taxonomy_custom_fields($term_id)
{
	if (! is_user_logged_in() || ! current_user_can('manage_categories')) {
		return;
	}

	if (isset($_POST['taxonomy_image']) && isset($_POST['_wpnonce'])) {
		if (!wp_verify_nonce($_POST['_wpnonce'], 'update-tag_' . $term_id)) {
			return;
		}

		update_term_meta($term_id, 'taxonomy_image', esc_url_raw($_POST['taxonomy_image']));
	}
}


// ---------------------------------------------------------------------------
// END - TAXONOMY IMAGE UPLOADER

// START - TAXONOMY CUSTOM URL FIELD
// ---------------------------------------------------------------------------
// Add meta box to taxonomy term edit screen
add_action('course_category_edit_form_fields', 'add_taxonomy_custom_url_field');
function add_taxonomy_custom_url_field($term)
{
?>
	<table class="form-table" role="presentation">
		<tbody>
			<tr class="form-field form-required term-name-wrap">
				<th scope="row"><label for="custom_url">Custom URL</label></th>
				<td>
					<input type="text" name="custom_url" id="custom_url" class="custom-url-field" value="<?php echo esc_attr(get_term_meta($term->term_id, 'custom_url', true)); ?>" style="width: 100%;">
					<p class="description">Use this field to overwrite the URL to link to instead of the category link. Enter the desired URL here.</p>
				</td>
			</tr>
		</tbody>
	</table>
<?php
}

// Save custom field data
add_action('edited_course_category', 'save_taxonomy_custom_url_field');
function save_taxonomy_custom_url_field($term_id)
{
	if (!is_user_logged_in() || !current_user_can('manage_categories')) {
		return;
	}

	if (isset($_POST['custom_url']) && isset($_POST['_wpnonce'])) {
		if (!wp_verify_nonce($_POST['_wpnonce'], 'update-tag_' . $term_id)) {
			return;
		}

		update_term_meta($term_id, 'custom_url', esc_url_raw($_POST['custom_url']));
	}
}

// ---------------------------------------------------------------------------
// END - TAXONOMY CUSTOM URL FIELD