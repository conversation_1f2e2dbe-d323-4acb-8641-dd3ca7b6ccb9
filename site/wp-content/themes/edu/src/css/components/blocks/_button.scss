.wawButton {
	.wp-block-button__link.wp-element-button {
		&.has-white-background-color {
			&:hover {
				background-color: rgba(255, 255, 255, 0.5);
				color: #fff;
			}
		}
	}
	&.is-style-outline {
		.wp-block-button__link.wp-element-button {
			border: 2px solid var(--wp--preset--color--orange);
			background-color: transparent;
			color: var(--wp--preset--color--orange);
			padding-left: 40px;
			padding-right: 40px;
			transition: background 0.2s ease-out;

			// font does not align
			// so we might need to
			// use the hack below
			// &::before {
			// 	content: '';
			// 	display: block;
			// 	height: 4px;
			// }

			&:hover {
				background-color: var(--wp--preset--color--orange);
				color: #fff;
			}
		}
	}
}

.wp-block-button__width-75 {
	.wp-block-button__link {
		min-width: 150px;
	}
}
