/*!****************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/sass-loader/dist/cjs.js!./src/css/main.scss ***!
  \****************************************************************************************************************************************/
/* HTML5 display-role reset for older browsers */
p {
  margin: 0;
  padding: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

.wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color:hover {
  background-color: rgba(255, 255, 255, 0.5);
  color: #fff;
}
.wp-block-button.is-style-outline .wp-block-button__link.wp-element-button {
  border: 2px solid var(--wp--preset--color--orange);
  background-color: transparent;
  color: var(--wp--preset--color--orange);
  padding-left: 40px;
  padding-right: 40px;
  transition: background 0.2s ease-out;
}
.wp-block-button.is-style-outline .wp-block-button__link.wp-element-button:hover {
  background-color: var(--wp--preset--color--orange);
  color: #fff;
}

.wp-block-button__width-75 .wp-block-button__link {
  min-width: 150px;
}

body .wp-block-button.is-style-outline.hover-variant-1 .wp-block-button__link.wp-element-button:hover {
  border: 1px solid #fff !important;
  background-color: #fff !important;
  color: #000 !important;
}

.site-header .container {
  position: relative;
}

body .wp-block-navigation__responsive-dialog {
  position: static;
  height: 100%;
}

body .wp-block-navigation {
  width: 100%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
}
@media (max-width: 768px) {
  body .wp-block-navigation {
    justify-content: flex-end;
  }
}
body .wp-block-navigation__responsive-container-open {
  z-index: 1;
}

body ul.wp-block-navigation__container {
  position: static;
  justify-content: flex-end;
}
@media (max-width: 768px) {
  body ul.wp-block-navigation__container {
    flex-direction: column;
    flex-wrap: nowrap;
    padding: 0 2rem;
  }
  body ul.wp-block-navigation__container .wp-block-navigation-submenu {
    padding: 0 !important;
  }
  body ul.wp-block-navigation__container li {
    height: auto !important;
  }
}

body .wp-block-navigation .wp-block-navigation-item {
  position: static;
}

.wp-block-navigation__responsive-container-content,
.wp-block-navigation__responsive-container-content > ul,
.wp-block-navigation__responsive-container-content > ul > li {
  height: 100%;
}

.wp-block-navigation-submenu:hover::before {
  content: "";
  height: 80px;
  left: 0;
  right: 0;
  position: absolute;
  bottom: -25px;
}

.wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) {
  height: 100%;
  display: flex;
  align-items: center;
  border: 1px solid;
}

.wp-block-navigation__responsive-close {
  height: 100%;
}

body .wp-block-navigation .has-child:not(.open-on-click):hover > .wp-block-navigation__submenu-container {
  width: calc(100vw - 20px);
  position: absolute;
  left: 50%;
  right: 0;
  transform: translateX(-50%);
}
body .wawNavContainer {
  flex-grow: 1;
}
body .wawNavContainer a {
  padding-left: 0;
}

.wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) {
  border: none;
}

@media (max-width: 1200px) {
  body nav.wp-block-navigation .wp-block-navigation__responsive-container-open {
    display: flex;
  }
  body nav.wp-block-navigation .wp-block-navigation__responsive-container {
    color: red;
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
  }
  body nav.wp-block-navigation .wp-block-navigation__responsive-container:not(.hidden-by-default):not(.is-menu-open) {
    border: none;
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
  }
  body nav.wp-block-navigation .wp-block-image {
    margin-left: 0 !important;
    margin-right: auto !important;
  }
  body nav.wp-block-navigation .wp-block-navigation__responsive-container-content {
    align-items: flex-start !important;
  }
  body nav.wp-block-navigation .wp-block-buttons {
    flex-direction: column;
    margin-left: 0;
    margin-bottom: 30px;
    align-content: flex-start;
  }
  body nav.wp-block-navigation .wp-block-navigation-submenu > a {
    font-weight: 700;
    font-size: 22px;
  }
  body nav.wp-block-navigation .wp-block-navigation-submenu .wawNavContainer .wp-block-columns {
    flex-wrap: wrap;
  }
  body nav.wp-block-navigation .wp-block-navigation-submenu .wawNavContainer .wp-block-columns .wp-block-column {
    text-align: left;
  }
  body nav.wp-block-navigation .wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation__container,
  body nav.wp-block-navigation .wp-block-navigation__responsive-container.is-menu-open .wp-block-navigation__responsive-container-content .wp-block-navigation-item {
    align-items: flex-start;
  }
}
@media (min-width: 765px) {
  .wawNavigation ul li.has-dropdown:after {
    content: "";
    background: url("../images/chevron-down.svg") no-repeat center center;
    width: 20px;
    height: 20px;
    display: inline-table;
    vertical-align: sub;
  }
}
.gform-theme--foundation .gform_fields {
  gap: 20px;
}

.gform-theme--foundation .gform-grid-col {
  padding-inline: 10px;
}

.gform_fields .nice-select,
.gform_fields select,
.gform_fields input[type=email],
.gform_fields input[type=tel],
.gform_fields input[type=text],
.gform_fields textarea {
  border-radius: 4px;
  border: 1px;
  box-shadow: none;
  height: 48px;
  outline: none;
  border: 1px solid #cbcbcb;
  line-height: 48px;
}

.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn)) > button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit], [type=button], [type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit], [type=button], [type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) {
  width: 320px;
  height: 48px;
  border-radius: 6px;
  margin: 0 auto;
  background-color: #d45e2b;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0.03em;
  text-align: center;
}

.gfield--width-full.shorten {
  width: 95%;
  margin: 0 auto;
  margin-top: 40px;
}
.gfield--width-full.shorten .ginput_container.ginput_container_radio {
  padding-left: 20px;
}

.gfield_label.gform-field-label {
  margin-bottom: 20px;
}

.gfield_radio {
  gap: 20px !important;
}

.gform-theme--framework .gfield--input-type-datepicker .ginput_container_date input {
  width: 100%;
}

.gform-theme--framework .gfield--type-choice.field_description_above.gfield--no-description .gform-field-label:where(:not([class*=gform-field-label--type-])),
.gform-theme--framework .gfield--type-choice.field_description_below .gform-field-label:where(:not([class*=gform-field-label--type-])) {
  font-size: 13px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0em;
  text-align: left;
  margin-bottom: 25px;
}

.nice-select {
  position: relative;
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-radius: 5px;
  border: solid 1px #e0e7ee;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  font-family: inherit;
  font-size: 14px;
  font-weight: normal;
  line-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 30px;
  position: relative;
  text-align: left !important;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
}

.nice-select:hover {
  border-color: #d0dae5;
}

.nice-select:active,
.nice-select.open,
.nice-select:focus {
  border-color: #88bfff;
}

.nice-select:after {
  border-bottom: 2px solid #90a1b5;
  border-right: 2px solid #90a1b5;
  content: "";
  display: block;
  height: 5px;
  margin-top: -4px;
  pointer-events: none;
  position: absolute;
  right: 12px;
  top: 50%;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  width: 5px;
}

.nice-select.open:after {
  -webkit-transform: rotate(-135deg);
  -ms-transform: rotate(-135deg);
  transform: rotate(-135deg);
}

.nice-select.open .list {
  opacity: 1;
  pointer-events: auto;
  -webkit-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: #e7ecf2;
  color: #90a1b5;
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: #cdd5de;
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .list {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .list {
  left: auto;
  right: 0;
}

.nice-select.small {
  font-size: 12px;
  height: 36px;
  line-height: 34px;
}

.nice-select.small:after {
  height: 4px;
  width: 4px;
}

.nice-select.small .option {
  line-height: 34px;
  min-height: 34px;
}

.nice-select .list {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 88, 112, 0.11);
  box-sizing: border-box;
  margin-top: 4px;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  -webkit-transform-origin: 50% 0;
  -ms-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -webkit-transform: scale(0.75) translateY(-21px);
  -ms-transform: scale(0.75) translateY(-21px);
  transform: scale(0.75) translateY(-21px);
  -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 9;
}

.nice-select .list:hover .option:not(:hover) {
  background-color: transparent !important;
}

.nice-select .option {
  cursor: pointer;
  font-weight: 400;
  line-height: 40px;
  list-style: none;
  min-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus {
  background-color: #f6f7f9;
}

.nice-select .option.selected {
  font-weight: bold;
}

.nice-select .option.disabled {
  background-color: transparent;
  color: #90a1b5;
  cursor: default;
}

.nice-select {
  /* Code blocks */
  /* Inline code */
}
.nice-select .no-csspointerevents .nice-select .list {
  display: none;
}
.nice-select .no-csspointerevents .nice-select.open .list {
  display: block;
}
.nice-select code[class*=language-],
.nice-select pre[class*=language-] {
  border-radius: 2px;
  color: #445870;
  -webkit-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  text-align: left;
  white-space: pre;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  direction: ltr;
  font-family: Inconsolata, monospace;
  font-size: 13px;
  letter-spacing: 0;
}
.nice-select pre[class*=language-] {
  padding: 18px 24px;
  margin: 0 0 24px;
  overflow: auto;
}
.nice-select :not(pre) > code[class*=language-],
.nice-select pre[class*=language-] {
  background: #f6f7f9;
}
.nice-select :not(pre) > code[class*=language-] {
  padding: 0 2px 1px;
}
.nice-select .token.comment,
.nice-select .token.prolog,
.nice-select .token.doctype,
.nice-select .token.cdata {
  color: #90a1b5;
}
.nice-select .token.punctuation {
  color: #999;
}
.nice-select .namespace {
  opacity: 0.7;
}
.nice-select .token.property,
.nice-select .token.tag,
.nice-select .token.boolean,
.nice-select .token.number,
.nice-select .token.constant,
.nice-select .token.symbol,
.nice-select .token.deleted {
  color: #ec4444;
}
.nice-select .token.selector,
.nice-select .token.attr-name,
.nice-select .token.string,
.nice-select .token.char,
.nice-select .token.builtin,
.nice-select .token.inserted {
  color: #4abf60;
}
.nice-select .token.operator,
.nice-select .token.entity,
.nice-select .token.url,
.nice-select .language-css .token.string,
.nice-select .style .token.string {
  color: #a67f59;
  background: rgba(255, 255, 255, 0.5);
}
.nice-select .token.atrule,
.nice-select .token.attr-value,
.nice-select .token.keyword {
  color: #55a1fb;
}
.nice-select .token.function {
  color: #dd4a68;
}
.nice-select .token.regex,
.nice-select .token.important,
.nice-select .token.variable {
  color: #e90;
}
.nice-select .token.important,
.nice-select .token.bold {
  font-weight: bold;
}
.nice-select .token.italic {
  font-style: italic;
}
.nice-select .token.entity {
  cursor: help;
}
.nice-select body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #445870;
  font-family: "Work Sans", sans-serif;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: -0.25px;
  margin: 0;
  padding: 0 18px;
}
.nice-select p {
  line-height: 1.6;
  margin: 0 0 1.6em;
}
.nice-select h1 {
  font-size: 36px;
  font-weight: 300;
  letter-spacing: -2px;
  margin: 0 0 24px;
}
.nice-select h2 {
  font-size: 22px;
  font-weight: 400;
  margin: 0 0 12px;
  padding-top: 48px;
}
.nice-select h3 {
  font-size: 18px;
  font-weight: 400;
  margin: 0 0 12px;
  padding-top: 12px;
}
.nice-select ul {
  margin: 0;
  padding-left: 16px;
}
.nice-select a:not(.button) {
  color: #55a1fb;
  outline: none;
  text-decoration: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border-bottom: 1px solid transparent;
}
.nice-select a:not(.button):hover,
.nice-select a:not(.button):focus {
  border-bottom: 1px solid #88bfff;
}
.nice-select ::-moz-selection {
  background: #f3f4f7;
}
.nice-select ::selection {
  background: #f3f4f7;
}
.nice-select .container {
  margin: 96px auto 60px;
  max-width: 40em;
}
.nice-select .box {
  background-color: #f6f7f9;
  border-radius: 2px;
  margin-bottom: 30px;
  padding: 24px 30px;
}
.nice-select .box:before,
.nice-select .box:after {
  content: "";
  display: table;
}
.nice-select .box:after {
  clear: both;
}
.nice-select label {
  color: #90a1b5;
  font-size: 11px;
  margin: 0 2px 4px;
  text-transform: uppercase;
  float: left;
}
.nice-select label.right {
  float: right;
}
.nice-select .button {
  -webkit-tap-highlight-color: transparent;
  background-color: #55a1fb;
  border-radius: 5px;
  border: none;
  box-sizing: border-box;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-weight: 600;
  height: 42px;
  line-height: 42px;
  outline: none;
  padding: 0 24px;
  text-align: center;
  text-decoration: none;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  width: auto;
}
.nice-select .button:hover,
.nice-select .button:focus {
  background-color: #4196fb;
}
.nice-select .button:active,
.nice-select .button.nice-select.open {
  background-color: #2d8bfa;
}
.nice-select .button.light {
  background-color: #fff;
  border: 1px solid #e0e7ee;
  color: #55a1fb;
  line-height: 40px;
  margin-left: 24px;
}
.nice-select .button.light:hover {
  border-color: #d0dae5;
}
.nice-select .button.light:active,
.nice-select .button.light.nice-select.open,
.nice-select .button.light:focus {
  border-color: #88bfff;
}
@media screen and (max-width: 360px) {
  .nice-select .button {
    width: 100%;
  }
  .nice-select .button.light {
    margin: 18px 0 0;
  }
}
.nice-select .header {
  text-align: center;
  margin-bottom: 60px;
}
@media screen and (min-width: 600px) {
  .nice-select .header {
    padding: 0 18px;
  }
}
.nice-select .header p {
  color: #90a1b5;
  font-size: 18px;
  margin-bottom: 36px;
}
.nice-select .footer {
  text-align: center;
}
.nice-select .footer p {
  margin-bottom: 90px;
}
.nice-select .credit {
  color: #90a1b5;
  clear: both;
  font-size: 12px;
  margin-top: 90px;
}

body p.is-style-pill {
  font-weight: 400;
  font-size: 17px;
  letter-spacing: 0;
  line-height: 42px;
  border-radius: 30px;
  text-transform: capitalize;
  background-color: #C6E2E2;
  color: #111111;
  height: 40px;
}

.wawHeaderMobile {
  width: 100%;
  height: 100%;
  overflow: scroll;
  background-color: #fff;
  padding: 20px 20px;
  overflow: unset;
}
.wawHeaderMobile button {
  border: none;
  background: none;
  outline: none;
}
.wawHeaderMobile--subMenu {
  position: fixed;
  top: 0;
  z-index: 9999;
  transform: translate(0px, -5000px);
  overflow-x: scroll;
}
.wawHeaderMobile--subMenu nav .wp-block-buttons {
  margin-left: 0;
}
.wawHeaderMobile--subMenu .wawHeaderMobile__wrapper {
  margin-top: 20px;
}
.wawHeaderMobile--subMenu .wawContainer__content .container {
  padding: 0;
}
@media (min-width: 1281px) {
  .wawHeaderMobile--subMenu .wawContainer__content .container {
    padding: 0 20px;
  }
}
.wawHeaderMobile--subMenu .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
  padding-left: 0;
}
@media (min-width: 1281px) {
  .wawHeaderMobile--subMenu .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    padding-left: 20px;
  }
}
.wawHeaderMobile--subMenu .wawHeaderMobile__logo .wp-block-site-logo {
  padding: 0;
}
.wawHeaderMobile--subMenu .wawHeaderMobile__logo .wp-block-site-logo.is-default-size img {
  width: unset;
}
.wawHeaderMobile--subMenu .wawHeaderMobile__logo img {
  max-height: 51px;
}
.wawHeaderMobile--subMenu .wawNavigation {
  margin: 40px 0;
}
.wawHeaderMobile--subMenu .wawNavigation .wp-block-navigation__responsive-container-open {
  display: none;
}
.wawHeaderMobile--subMenu .wawNavigation nav {
  display: block;
}
.wawHeaderMobile--subMenu .wawNavigation nav ul {
  gap: 0;
}
.wawHeaderMobile--subMenu .wawNavigation nav a {
  font-size: 14px;
  font-weight: 500;
  line-height: 23.52px;
  text-align: left;
  padding-left: 0;
  text-transform: uppercase;
}
.wawHeaderMobile--subMenu .wp-block-buttons {
  flex-direction: column;
}
.wawHeaderMobile--subMenu .wp-block-buttons .wp-block-button {
  width: 100%;
}
.wawHeaderMobile--subMenu .wp-block-buttons .wp-block-button a {
  width: inherit;
}
.wawHeaderMobile--subMenu .wp-block-buttons .wp-block-button:last-child {
  margin-bottom: 30px;
}
.wawHeaderMobile__search--form {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 9999;
  transform: translate(0px, -5000px);
}
.wawHeaderMobile__search--form .wawSearchFiltered {
  display: block;
}
.wawHeaderMobile__search .wawSearchFiltered {
  background: none;
  flex-direction: column;
  padding: 0;
}
.wawHeaderMobile__search .wawSearchFiltered input[type=search],
.wawHeaderMobile__search .wawSearchFiltered .nice-select {
  width: 100%;
  box-shadow: 0px 0px 8.5px 0px rgba(0, 0, 0, 0.1019607843);
  border: 1px solid #e5e5e5;
  margin-bottom: 10px;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
}
.wawHeaderMobile__search .wawSearchFiltered .nice-select {
  margin-bottom: 20px;
}
.wawHeaderMobile__search .wawSearchFiltered .wawSearchFiltered__inputs {
  flex-direction: column;
}
.wawHeaderMobile__search .wawSearchFiltered .wawSearchFiltered__buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.wawHeaderMobile__search .wawSearchFiltered .wawSearchFiltered__buttons a {
  color: #1e1e1e;
  text-decoration: underline !important;
  font-size: 15px;
}
.wawHeaderMobile__search .wawSearchFiltered button {
  position: relative;
  width: 150px;
  height: 46px;
  margin: 0;
}
.wawHeaderMobile__search .wawSearchFiltered button span {
  display: block;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 23.04px;
  text-align: center;
}
.wawHeaderMobile__search .wawSearchFiltered button svg {
  display: none;
}
.wawHeaderMobile__search--trigger {
  position: sticky;
  top: 0;
  z-index: 9;
  background: #fff;
  padding: 0 40px;
}
.wawHeaderMobile__search--trigger input {
  pointer-events: none;
}
@media (max-width: 600px) {
  .wawHeaderMobile__search--trigger {
    padding: 0 0;
  }
}
.wawHeaderMobile__search input {
  box-shadow: 0px 0px 8.5px 0px rgba(0, 0, 0, 0.1019607843);
  border: 1px solid #e5e5e5;
  width: 100%;
  height: 54px;
  border-radius: 8px;
  padding: 0 15px;
  background: none;
}
.wawHeaderMobile__search button {
  width: 100%;
  outline: none;
  background: none;
  border: none;
  position: relative;
  padding: 20px 0;
}
.wawHeaderMobile__search button div {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 10px;
  width: 45px;
  height: 40px;
  background-color: #1e1e1e;
  border-radius: 7px;
  outline: none;
  border: none;
  cursor: pointer;
}
.wawHeaderMobile__search-inner {
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  box-shadow: 0px 0px 8.5px 0px rgba(0, 0, 0, 0.1019607843);
  padding: 15px;
}
.wawHeaderMobile__search-inner h2 {
  margin: 0;
}
.wawHeaderMobile__search-inner .wawSearchFiltered__inputWrapper {
  width: 100% !important;
}
.wawHeaderMobile__search-header {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  margin-bottom: 20px;
  align-items: center;
}
.wawHeaderMobile__search-header button {
  margin: 0;
  width: unset;
}
.wawHeaderMobile__search-header svg {
  fill: #1e1e1e;
  width: 40px;
  height: auto;
}
@media (min-width: 1281px) {
  .wawHeaderMobile__search {
    display: none;
  }
}
@media (max-width: 600px) {
  .wawHeaderMobile {
    padding: 0;
  }
}
@media (min-width: 768px) {
  .wawHeaderMobile {
    display: none;
  }
}

.wawHeaderDesktop {
  display: none;
  padding: 20px 40px;
}
.wawHeaderDesktop select {
  background: none;
  border: none;
}
@media (min-width: 768px) {
  .wawHeaderDesktop {
    display: block;
  }
  .wawHeaderDesktop .wawSearchFiltered__buttons-clear {
    display: none;
  }
  .wawHeaderDesktop .wawSearchFiltered__buttons span {
    display: none;
  }
}

.header-interior-page {
  background-color: var(--wp--preset--color--primary);
}

.image-inner {
  position: absolute;
  top: 74%;
  right: -20%;
  z-index: 2;
}

.wp-block-query.is-style-two-columns ul li {
  margin-bottom: 0;
}

.wp-block-query .campus {
  display: flex;
  flex-direction: column;
}
.wawSearchCourses input[type=search]::placeholder {
  font-size: 16px;
  font-family: var(--wp--preset--font-family--primary);
}

.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child {
  max-height: 70%;
  overflow-y: scroll;
  right: 5px !important;
}

.wp-swiper.is-style-overlayed-text-right .wp_swiper__navigation {
  bottom: -5%;
  right: 5%;
}

@media (max-width: 1280px) {
  .wp-swiper.is-style-thumbnails-bottom-right .wp-swiper__thumbs {
    position: relative !important;
    transform: unset !important;
    width: 100% !important;
    right: unset !important;
  }
}
.da-icontextblock.is-style-centered-showcase-box-style {
  margin: auto;
  box-shadow: 0 0 15px 5px rgba(0, 0, 0, 0.07);
  background: #000;
  border: none;
  position: relative;
  overflow: hidden;
  max-height: 463px;
  max-width: 460px;
  text-align: center;
  cursor: pointer;
  padding: 0;
  border-radius: 3px;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__image {
  position: relative;
  display: block;
  min-height: 100%;
  border-radius: 0;
  border: none;
  transform: none;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__image .da-icontextblock__image-link {
  display: block;
  width: 100%;
  height: 100%;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__image-sizer {
  height: 100%;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__image .da-icontextblock__image-element {
  opacity: 0.8;
  transition: opacity 0.35s, transform 0.35s;
  will-change: transform;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__content {
  padding: 1em;
  color: #fff;
  text-transform: uppercase;
  font-size: 1.25rem;
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: justify-content 0.35s ease;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__content::before {
  position: absolute;
  content: "";
  opacity: 0;
  top: 50px;
  right: 30px;
  bottom: 50px;
  left: 30px;
  border-top: 2px solid rgba(255, 255, 255, 0.3);
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
  transform: scale(0, 1);
  transform-origin: 0 0;
  transition: opacity 0.35s, transform 0.35s;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__content::after {
  position: absolute;
  content: "";
  opacity: 0;
  top: 30px;
  right: 50px;
  bottom: 30px;
  left: 50px;
  border-right: 1px solid #fff;
  border-left: 1px solid #fff;
  transform: scale(1, 0);
  transform-origin: 100% 0;
  transition: opacity 0.35s, transform 0.35s;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__header {
  transition: transform 0.35s;
  transform: translate3d(0, -30px, 0);
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__title {
  color: #fff;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  text-transform: capitalize;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__title .da-icontextblock__link {
  color: inherit;
  text-decoration: none;
  display: inline-block;
  transition: color 0.3s ease;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__title .da-icontextblock__link:hover {
  color: rgba(255, 255, 255, 0.8);
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__body {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  padding: 0.5em 2.7em;
  text-transform: none;
  opacity: 0;
  transform: translate3d(0, -10px, 0);
  font-size: 0.92rem;
  line-height: 16px;
  font-family: Poppins;
  transition: opacity 0.35s, transform 0.35s;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__button {
  margin-top: 20px;
  text-align: center;
  z-index: 1000;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.5s ease-in-out;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__button .da-icontextblock__button-element {
  display: inline-block;
  padding: 8px 20px;
  color: #fff;
  text-decoration: none;
  border-radius: 3px;
  font-size: 16px;
  text-transform: capitalize;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}
.da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__button .da-icontextblock__button-element:hover {
  color: rgba(255, 255, 255, 0.8);
}
.da-icontextblock.is-style-centered-showcase-box-style:hover .da-icontextblock__image-element {
  opacity: 0.7;
  transform: scale(1.1);
}
.da-icontextblock.is-style-centered-showcase-box-style:hover .da-icontextblock__content::before, .da-icontextblock.is-style-centered-showcase-box-style:hover .da-icontextblock__content::after {
  opacity: 1;
  transform: scale(1);
  transition-delay: 0.15s;
}
.da-icontextblock.is-style-centered-showcase-box-style:hover .da-icontextblock__header,
.da-icontextblock.is-style-centered-showcase-box-style:hover .da-icontextblock__body {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition-delay: 0.15s;
}
.da-icontextblock.is-style-centered-showcase-box-style:hover .da-icontextblock__button {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.3s;
}
@media (max-width: 575px) {
  .da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__title {
    font-size: 1.3rem;
  }
}
@media (min-width: 576px) and (max-width: 767px) {
  .da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__title {
    font-size: 1.3rem;
    font-family: Oswald;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .da-icontextblock.is-style-centered-showcase-box-style .da-icontextblock__title {
    font-size: 1.3rem;
  }
}

.wawAccordion .da-accordion__item-content {
  padding: 0 80px 20px 40px;
}
.wawAccordion .da-accordion__item-title {
  font-size: 24px;
}
.wawAccordion .da-accordion__item-title:hover {
  color: #000;
}
.wawAccordion .da-accordion__item > a {
  font-weight: 400;
  padding: 40px 60px 40px 0;
}

.hello-bar a {
  color: #111111;
}

@media (min-width: 1280px) {
  .p-10px-gt-1280 {
    padding: 10px !important;
  }
  .p-20px-gt-1280 {
    padding: 20px !important;
  }
  .gap-20px-gt-1280 {
    gap: 20px !important;
  }
  .gap-100px-gt-1280 {
    gap: 100px !important;
  }
  .h-100-gt-1280 {
    height: 100% !important;
  }
}
@media (max-width: 1280px) {
  .fs-18px-lt-1280 {
    font-size: 18px !important;
  }
  .pl-0-lt-1280 {
    padding-left: 0 !important;
  }
  .p-0-lt-1280 {
    padding: 0 !important;
  }
  .px-0-lt-1280 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .px-20px-lt-1280 {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
  .py-0-lt-1280 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-40px-lt-1280 {
    padding-top: 40px !important;
    padding-bottom: 40px !important;
  }
  .p-10px-lt-1280 {
    padding: 10px !important;
  }
  .p-20px-lt-1280 {
    padding: 20px !important;
  }
  .p-30px-lt-1280 {
    padding: 30px !important;
  }
  .p-40px-lt-1280 {
    padding: 40px !important;
  }
  .p-50px-lt-1280 {
    padding: 50px !important;
  }
  .p-60px-lt-1280 {
    padding: 60px !important;
  }
  .my-auto-lt-1280,
  .ml-auto-mr-auto-lt-1280 {
    margin-left: auto;
    margin-right: auto;
  }
  .mx-0-lt-1280 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .mx-20px-lt-1280 {
    margin-top: 20px !important;
    margin-bottom: 20px !important;
  }
  .mx-40px-lt-1280 {
    margin-top: 40px !important;
    margin-bottom: 40px !important;
  }
  .m-0-lt-1280 {
    margin: 0 !important;
  }
  .mt-30px-lt-1280 {
    margin-top: 30px !important;
  }
  .mt-40px-lt-1280 {
    margin-top: 40px !important;
  }
  .mb-0-lt-1280 {
    margin-bottom: 0 !important;
  }
  .mb-40px-lt-1280 {
    margin-bottom: 40px !important;
  }
  .o-1-lt-1280 {
    order: 1 !important;
  }
  .o-2-lt-1280 {
    order: 2 !important;
  }
  .h-0-lt-1280 {
    height: 0 !important;
  }
  .h-20px-lt-1280 {
    height: 20px !important;
  }
  .h-40px-lt-1280 {
    height: 40px !important;
  }
  .h-60px-lt-1280 {
    height: 60px !important;
  }
  .gap-0-lt-1280 {
    gap: 0 !important;
  }
  .gap-20px-lt-1280 {
    gap: 20px !important;
  }
  .gap-40px-lt-1280 {
    gap: 40px !important;
  }
  .fd-column-lt-1280 {
    flex-direction: column !important;
  }
  .fd-row-lt-1280 {
    flex-direction: row !important;
  }
  .ta-left-lt-1280 {
    text-align: left !important;
  }
  .ta-center-lt-1280 {
    text-align: center !important;
  }
  .ta-right-lt-1280 {
    text-align: right !important;
  }
  .w-60-lt-1280 {
    width: 60%;
  }
  .w-80-lt-1280 {
    width: 80%;
  }
  .ai-fs-lt-1280 {
    align-items: flex-start !important;
  }
  .jc-fs-lt-1280 {
    justify-content: flex-start !important;
  }
  .gtc-1-1fr-lt-1280 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .gtc-2-1fr-lt-1280 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .mw-unset-lt-1280 {
    max-width: unset !important;
  }
}
* {
  box-sizing: border-box;
}

.wp-block-group.is-show-seperator > :not(:last-child):after {
  background-color: #111;
}

body {
  line-height: 25px;
  overflow-x: hidden;
  /* Ensure smooth font rendering across all browsers */
  -webkit-font-smoothing: antialiased; /* For WebKit browsers like Chrome and Safari */
  -moz-osx-font-smoothing: grayscale; /* For macOS and Firefox */
  font-smooth: always; /* General smooth rendering (some browsers support) */
  text-rendering: optimizeLegibility; /* Improve readability for larger bodies of text */
}
@media (min-width: 1400px) {
  body .container-xxl,
  body .container-xl,
  body .container-lg,
  body .container-md,
  body .container-sm,
  body .container {
    max-width: var(--wp--style--global--content-size);
  }
}
body.blog {
  background-color: #f5f5f5;
}
body.blog .wp-block-post-title {
  flex-basis: unset;
}

ol {
  list-style: auto;
}
ol li {
  margin-bottom: 20px;
}

.wp-swiper > .wp-swiper__wrapper {
  margin-bottom: 0 !important;
}

.wp-block-columns {
  max-width: var(--wp--style--global--content-size);
  margin: 0 auto;
}

.alignwide {
  max-width: var(--wp--style--global--wide-size);
}

a:hover {
  transition: color 0.2s ease-out;
  color: unset;
}

.site-header {
  position: relative;
  z-index: 10;
}
.site-header nav {
  white-space: nowrap;
}
.site-header nav .wp-block-navigation__container {
  gap: 30px;
}
.site-header nav .wp-block-navigation__container a:hover {
  color: var(--wp--preset--color--prmimary);
}
@media (max-width: 1281px) {
  .site-header .wp-block-site-logo {
    padding: 20px 0;
  }
}

footer a:hover {
  text-decoration: underline !important;
}
footer ul {
  padding-left: 0;
}
footer li {
  display: block;
  margin-bottom: 10px;
  line-height: 26px;
}
@media (max-width: 781px) {
  footer li {
    display: inline-block;
    width: 100%;
    float: left;
  }
}
@media (min-width: 781px) and (max-width: 1023px) {
  footer .footer-icons {
    display: none;
  }
}
footer .footer-icons .wp-block-column.footer-column-social-icon {
  max-width: fit-content;
}
@media (max-width: 781px) {
  footer .footer-icons .wp-block-column.footer-column-social-icon {
    margin: 0 20px 0 0;
  }
}
@media (max-width: 781px) {
  footer .wp-block-spacer:first-of-type {
    height: 60px !important;
  }
}
footer .site-by span {
  font-weight: bold;
}
.footer-wrap-group-logos.is-layout-flex {
  align-items: flex-start;
  flex-direction: row;
}

.wp-block-post-template .wp-block-post-featured-image {
  margin-bottom: 20px;
}
.wp-block-post-template-is-layout-grid .wp-block-post-excerpt__excerpt {
  font-weight: 400;
  font-size: 20px;
  line-height: 148%;
  letter-spacing: 0%;
}
.wp-block-post-template-is-layout-grid .wp-block-post-title {
  background: none;
  padding: 0;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
}
.wp-block-post-template-is-layout-grid .wp-block-post-title a {
  text-decoration: none;
  color: inherit;
}

.wawPostCards {
  color: red;
}

.wp-swiper .wp-block-cover {
  padding: 0;
}

.wp-swiper__navigation,
.wp_swiper__navigation {
  position: absolute;
  bottom: -10%;
  left: 0;
  right: 0;
}
.wp-swiper__navigation-container,
.wp_swiper__navigation-container {
  position: relative;
  margin: 0 auto;
  max-width: var(--wp--style--global--content-size);
}

.wp-swiper.is-style-thumbnails-bottom-right .wp_swiper__navigation .wp_swiper__button-next {
  left: calc(var(--swiper-navigation-size) + 31px);
}

.wp-swiper.is-style-thumbnails-bottom-right .wp_swiper__navigation .wp_swiper__button-prev {
  left: 11px;
}

.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-background) {
  border: 1px solid;
  border-color: var(--wp--preset--color--black);
}

.wp-block-site-logo {
  z-index: 1;
}

.wp-block-post-content {
  position: relative;
  background-color: #f5f5f5;
}

.wawEventHeader .wp-block-categories {
  padding-left: 0;
  font-size: 20px;
  font-weight: 400;
  line-height: 27px;
  letter-spacing: 0em;
  text-align: left;
  margin-bottom: 15px;
}
.wawEventHeader .wp-block-categories a {
  color: #fff;
}
.wawEventHeader .wp-block-post-title {
  color: #fff;
  font-size: 56px;
  font-weight: 800;
  line-height: 76px;
  letter-spacing: 0em;
  text-align: left;
}
@media (max-width: 768px) {
  .wawEventHeader .wp-block-post-title {
    font-size: 32px;
    line-height: 48px;
  }
}
.wawEventHeader .event-location {
  margin-right: 100px;
}
@media (max-width: 768px) {
  .wawEventHeader .event-location {
    margin-right: 20px;
  }
}
@media (max-width: 768px) {
  .wawEventHeader .wp-block-group {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
}
.wawEventHeader .event-date,
.wawEventHeader .event-location {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  letter-spacing: 0px;
  text-align: left;
}
.wawEventHeader .event-date span,
.wawEventHeader .event-location span {
  padding-top: 4px;
}
.wawEventHeader .event-date svg,
.wawEventHeader .event-location svg {
  margin-right: 10px;
}

.withWhitePanel {
  position: relative;
}
.withWhitePanel h2 {
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0em;
  text-align: left;
  background-color: #fff;
  padding: 20px 35px 20px 20px;
  margin: 0;
  margin-bottom: 0;
}
@media (max-width: 781px) {
  .withWhitePanel h2 {
    font-size: 16px !important;
  }
}
@media (min-width: 782px) and (max-width: 1440px) {
  .withWhitePanel h2 {
    font-size: 18px !important;
  }
}
.withWhitePanel a {
  text-decoration: none;
  color: inherit;
}
.withWhitePanel svg {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  max-width: 10px;
  transition: all 0.4s ease-out;
}
.withWhitePanel.withSideBorder {
  border-left: 8px solid #d45e2b !important;
}
.withWhitePanel.withSideBorder:hover a {
  color: #d45e2b;
}
.withWhitePanel.withSideBorder:hover svg {
  margin-right: 10px;
}

@media (max-width: 768px) {
  .wp-block-group {
    flex-direction: column;
  }
}
.wp-block-cover .wp-block-cover__inner-container {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.section-home-explore {
  padding: 40px 20px;
}

.thumbnail-link {
  color: blue !important;
}

/*************** Danilo CSS *********************/
p {
  margin-bottom: 21px;
}

.aligncenter {
  margin-left: auto;
  margin-right: auto;
}

.wawSubMenu hr {
  margin-right: 40%;
  margin-top: 10px !important;
  margin-bottom: 20px !important;
  display: block;
}

.wawSubMenu .wawSearchCourses input[type=search] {
  width: 100%;
  height: 40px;
  border-radius: 40px;
  box-shadow: unset;
  min-width: 320px;
  padding: 10px 30px;
  border: none;
  border: 1px solid #7c7c7c;
}
.wawSubMenu .wawSearchCourses input[type=search]:hover {
  border: 1px solid rgba(0, 0, 0, 0.2);
  outline: none;
  cursor: pointer;
}
.wawSubMenu .wawSearchCourses button {
  position: absolute;
  border: none;
  background: none;
  top: 50%;
  right: 0;
  cursor: pointer;
  transform: translateY(-50%);
  font-size: 0;
}
.wawSubMenu .wawSearchCourses button svg path:first-of-type {
  fill: #fff;
}
.wawSubMenu .wawSearchCourses button svg path:last-of-type {
  fill: #135758;
}

/*** Outline btn ***/
body .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button {
  display: flex;
  align-content: center;
  justify-content: center;
  flex-wrap: wrap;
  border: 1px solid;
  color: var(--wp--preset--color--primary);
  white-space: normal;
}
body .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button:hover {
  background-color: var(--wp--preset--color--primary) !important;
  border: 1px solid var(--wp--preset--color--primary) !important;
}
body .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button:active {
  background-color: var(--wp--preset--color--primary) !important;
  border: 1px solid var(--wp--preset--color--primary) !important;
}
body .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button:disabled {
  background-color: transparent !important;
  color: #66829c !important;
  border: 1px solid #66829c !important;
}

.wp-block-button.is-style-fill .wp-block-button__link.wp-element-button {
  display: flex;
  align-content: center;
  justify-content: center;
  flex-wrap: wrap;
  border: 1px solid;
  border-color: var(--wp--preset--color--primary);
}
.wp-block-button.is-style-fill .wp-block-button__link.wp-element-button:hover {
  border-color: var(--wp--preset--color--primary) !important;
  background-color: #fff !important;
  color: var(--wp--preset--color--primary) !important;
}

.wp-block-button.is-style-outline > .wp-block-button__link:not(.has-background) {
  border: 1px solid;
  border-color: var(--wp--preset--color--primary);
}

/*** has-white-background-color btn ***/
body .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color {
  display: flex;
  align-content: center;
  justify-content: center;
  flex-wrap: wrap;
  border: 1px solid;
  border-color: var(--wp--preset--color--white);
  color: var(--wp--preset--color--primary);
}
body .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color:hover {
  background-color: var(--wp--preset--color--primary) !important;
  border: 1px solid var(--wp--preset--color--primary) !important;
  color: #fff !important;
}
body .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color:active {
  background-color: var(--wp--preset--color--primary) !important;
  border: 1px solid var(--wp--preset--color--primary) !important;
}
body .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color:disabled {
  background-color: #e6e6e6 !important;
  color: #66829c !important;
  border: 1px solid #e6e6e6 !important;
}

/*********  HEADER BUTTON  *********/
.wawNavigation nav a.wp-element-button:hover, .wawNavigation nav a.wp-element-button:focus, .wawNavigation nav a.wp-element-button:active {
  color: var(--wp--preset--color--white) !important;
}

.wp-block-query .wp-block-post-featured-image {
  height: auto;
  flex-grow: 1;
}
.wp-block-query .wp-block-post-featured-image img {
  height: 100%;
}

body .wp-block-media-text.is-image-fill .wp-block-media-text__media img {
  width: 100%;
  height: auto;
  position: relative;
}
body .wp-block-media-text.has-media-on-the-right.is-stacked-on-mobile.is-image-fill h2 {
  margin-top: 0;
}

body .da-accordion__item-heading .svg svg,
body .da-accordion__item > a.active svg {
  stroke: #00549f;
}

.waw-tabs__background {
  background-color: var(--wp--preset--color--primary) !important;
  display: none;
}

.waw-tabs__header {
  max-width: 80%;
  margin-right: auto;
  margin-left: auto;
  background-color: white;
}
.waw-tabs__header h4 {
  transition: all 0.4s ease-out;
}
.waw-tabs__header .is-active {
  background-color: var(--wp--preset--color--primary) !important;
  border-radius: 10px;
}

.nav-header {
  background-color: #fff;
  padding: 15px 0;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.4705882353);
}

.second-li-list-style-none {
  margin-bottom: 15px;
}
.second-li-list-style-none li {
  margin-bottom: 0;
}
.second-li-list-style-none li:nth-child(2) {
  list-style: none;
}

.hentry.event_category-event-category .wawEventMeta:hover svg {
  fill: #d45e2b;
}
.hentry.event_category-event-category .wawEventMeta .wawEventMeta__link svg {
  fill: #d45e2b;
}

.wawEventMeta__link svg {
  fill: #d45e2b;
}
.wawEventMeta__link:hover svg {
  fill: #d45e2b;
}

.da-icontextblock.is-style-boxed-content-centered {
  margin-bottom: 20px;
  padding: 0;
  background: none;
}
.da-icontextblock.is-style-boxed-content-centered .da-icontextblock__title {
  margin-top: 20px;
}
.da-icontextblock.is-style-boxed-content-centered .da-icontextblock__image {
  border-radius: unset;
  border: none;
  overflow: hidden;
}
.da-icontextblock.is-style-boxed-content-centered .da-icontextblock__image img {
  width: 100%;
}

@media (max-width: 768px) {
  .wp-block-columns {
    flex-wrap: wrap !important;
  }
}
@media (max-width: 768px) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    flex-basis: 100% !important;
  }
}
@media (min-width: 767px) {
  .wp-block-columns:not(.is-not-stacked-on-mobile) > .wp-block-column {
    flex-basis: 0;
    flex-grow: 1;
    margin: 0 10px;
  }
}

.border-bottom-radius-12 {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.container-padding-single-post .wawContainer__content .container {
  padding: 0 100px;
}
.container-padding-single-post .wawContainer__content .container .wp-block-image img {
  border-radius: 12px;
}

.container-wrapper-form .wawContainer__content .container {
  max-width: 800px;
}

.img-top-radius-12px img {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.img-radius-12px img {
  border-radius: 12px;
}

.wp-block-cover .has-background-dim:not([class*=-background-color]),
.wp-block-cover-image .has-background-dim:not([class*=-background-color]),
.wp-block-cover-image.has-background-dim:not([class*=-background-color]),
.wp-block-cover.has-background-dim:not([class*=-background-color]) {
  background-color: unset;
}

.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn)) > button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit], [type=button], [type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit], [type=button], [type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)),
.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)) {
  width: 320px;
  height: 48px;
  margin-left: 0;
  background: none;
  border-radius: 6px;
  border-radius: 4px;
  border-width: 1.5px;
  border-color: #135758;
  color: #135758;
  font-size: 16px;
  font-weight: 400;
  line-height: 19px;
  letter-spacing: 0.03em;
  text-align: center;
}
.gform-theme.gform-theme--framework.gform_wrapper .button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,
.gform-theme.gform-theme--framework.gform_wrapper .gform-theme-button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,
.gform-theme.gform-theme--framework.gform_wrapper :where(:not(.mce-splitbtn)) > button:not([id*=mceu_]):not(.mce-open):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,
.gform-theme.gform-theme--framework.gform_wrapper button.button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,
.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit], [type=button], [type=reset]).button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,
.gform-theme.gform-theme--framework.gform_wrapper input:is([type=submit], [type=button], [type=reset]):where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover,
.gform-theme.gform-theme--framework.gform_wrapper input[type=submit].button.gform_button:where(:not(.gform-theme-no-framework):not(.gform-theme__disable):not(.gform-theme__disable *):not(.gform-theme__disable-framework):not(.gform-theme__disable-framework *)):hover {
  background-color: #135758;
  color: #fff;
}

@media (max-width: 767px) {
  form.wp-container-content-1 {
    flex-basis: unset;
  }
}

.wp-block-buttons > .wp-block-button.wp-block-button__width-50 {
  width: initial;
}
@media (min-width: 768px) {
  .wp-block-buttons > .wp-block-button.wp-block-button__width-50 {
    width: calc(50% - var(--wp--style--block-gap, 0.5em) * 0.5);
  }
}

/**************  OVERWRITING ***********/
@media (min-width: 100px) {
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: 100%;
  }
}
@media (min-width: 992px) {
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: 1200px;
  }
}

@media (max-width: 781px) {
  .ready-to-get-started-section .is-content-justification-right,
  .ready-to-get-started-section .is-content-justification-left {
    justify-content: center;
    padding-bottom: 15px;
  }
}
@media (max-width: 781px) {
  .ready-to-get-started-section .wp-block-buttons > .wp-block-button.wp-block-button__width-50 {
    width: 90% !important;
  }
}

.site-footer a {
  text-decoration: none;
}
@media (max-width: 781px) {
  .site-footer h5 {
    margin-top: 0;
  }
}

@media (max-width: 781px) {
  .container-wrapper-form .wawContainer.is-style-with-border .wawContainer__content {
    padding: 49px 10px;
  }
}
@media (max-width: 781px) {
  .container-wrapper-form .col {
    padding: 0;
  }
}

.entry-content ul:not(.wp-block-post-template) ol,
.entry-content ul:not(.wp-block-post-template) li {
  list-style: initial;
}
@media (max-width: 1200px) {
  .entry-content ul:not(.wp-block-post-template) {
    padding-top: 0 !important;
  }
}

@media (min-width: 1024px) and (max-width: 1200px) {
  .container-top-form-text .container {
    max-width: 82%;
  }
}
@media (min-width: 1201px) and (max-width: 1400px) {
  .container-top-form-text .container {
    max-width: 72%;
  }
}
@media (min-width: 1401px) {
  .container-top-form-text .container {
    max-width: 62%;
  }
}

@media (max-width: 782px) {
  h2 {
    font-size: 32px !important;
  }
}

@media (max-width: 1024px) {
  .wawContainer.is-style-with-border.wrapper-contact-form .wawContainer__content {
    padding: 40px 15px;
  }
}

@media (max-width: 782px) {
  .h2-contact-form {
    margin-bottom: 0;
  }
}

@media (max-width: 781px) {
  .info-sidebar .is-layout-flex {
    display: inline-block;
    text-align: left;
  }
}
@media (max-width: 781px) {
  .info-sidebar .is-layout-flex .wawSvgIcon,
  .info-sidebar .is-layout-flex p {
    float: left;
  }
}
@media (max-width: 781px) {
  .info-sidebar h5 {
    margin-bottom: 30px !important;
  }
}
@media (max-width: 1024px) {
  .info-sidebar.wawContainer.is-style-with-border .wawContainer__content {
    padding: 40px 15px;
  }
}

@media (max-width: 781px) {
  .is-style-overlayed-text-right .wp-swiper__slide-content {
    display: block;
  }
}
@media (max-width: 781px) {
  .is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column,
  .is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child {
    width: 100%;
    max-width: 100% !important;
    display: block;
    position: relative !important;
    padding: 0 !important;
    margin-right: 0 !important;
  }
}
@media (max-width: 781px) {
  .is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child {
    padding: 20px !important;
  }
}

@media (max-width: 1200px) {
  .container-bg-locations .wp-block-query.is-style-two-columns ul {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 781px) {
  .container-bg-locations .is-content-justification-right,
  .container-bg-locations .is-layout-flex.wp-block-buttons-is-layout-flex {
    justify-content: center;
  }
}

@media (max-width: 781px) {
  .hide-mobile {
    display: none;
  }
}
@media (min-width: 782px) {
  .hide-mobile {
    display: block;
  }
}

@media (max-width: 781px) {
  .show-mobile {
    display: block;
  }
}
@media (min-width: 782px) {
  .show-mobile {
    display: none;
  }
}

@media (max-width: 781px) {
  .course-list.wp-block-query.is-style-two-columns ul {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 781px) {
  .course-list h2.wp-block-post-title {
    font-size: 16px !important;
  }
}
@media (min-width: 782px) and (max-width: 1440px) {
  .course-list h2.wp-block-post-title {
    font-size: 18px !important;
  }
}

@media (max-width: 781px) {
  .section-tiles-home-page .wp-block-buttons {
    margin-bottom: 0 !important;
  }
}
@media (max-width: 781px) {
  .section-tiles-home-page .is-style-80-gap {
    gap: 0 !important;
    padding-bottom: 60px !important;
  }
}

.column-section-map-content .link-with-arrow {
  flex-direction: row;
}

iframe {
  width: 100%;
}

@media (max-width: 781px) {
  body .wp-swiper.is-style-thumbnails-bottom-right .wp-swiper__thumbs {
    width: 80%;
  }
}

/*****************  TABLE  ************************/
.wawCoursesTable {
  margin-bottom: 0 !important;
}
.wawCoursesTable table {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
}
.wawCoursesTable table caption {
  font-size: 1.5em;
  margin: 0.5em 0 0.75em;
}
.wawCoursesTable table tr {
  border: 1px solid #ddd;
  padding: 0.35em;
}
.wawCoursesTable table th,
.wawCoursesTable table td {
  padding: 0.625em;
  text-align: center;
}
.wawCoursesTable table th {
  font-size: 0.85em;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}
@media screen and (max-width: 781px) {
  .wawCoursesTable table {
    border: 0;
  }
  .wawCoursesTable table caption {
    font-size: 1.3em;
  }
  .wawCoursesTable table thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    color: #000 !important;
  }
  .wawCoursesTable table tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }
  .wawCoursesTable table td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 0.8em;
    text-align: right !important;
  }
  .wawCoursesTable table td.table-title {
    text-align: left !important;
  }
  .wawCoursesTable table td::before {
    /*
     * aria-label has no advantage, it won't be read inside a table
     content: attr(aria-label);
     */
    content: attr(data-label);
    float: left;
    font-weight: bold;
    text-transform: uppercase;
  }
  .wawCoursesTable table td:last-child {
    border-bottom: 0;
  }
}

/******************** NEW TABLES  *****************/
.banner-text-btn .wp-block-button .wp-block-button__link.wp-element-button.has-white-background-color:hover {
  border: 1px solid #fff !important;
}

.courses-list-block.wp-block-query.is-style-two-columns ul {
  display: grid;
  grid-auto-rows: minmax(min-content, max-content);
  grid-template-columns: repeat(3, 1fr);
}
@media (min-width: 782px) and (max-width: 1023px) {
  .courses-list-block.wp-block-query.is-style-two-columns ul {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 781px) {
  .courses-list-block.wp-block-query.is-style-two-columns ul {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 1400px) {
  .courses-list-block.wp-block-query.is-style-two-columns ul .wp-block-post-title {
    font-size: 16px !important;
  }
}

.btn-link-text-plus-arrow {
  display: flex !important;
  flex-direction: row !important;
}

@media (max-width: 781px) {
  .section-image-right .wp-block-heading {
    font-size: 32px !important;
    text-align: left;
  }
}
@media (max-width: 781px) {
  .section-image-right p {
    max-width: 100% !important;
  }
}
@media (max-width: 781px) {
  .section-image-right ul {
    max-width: 100% !important;
  }
}
@media (max-width: 781px) {
  .section-image-right ul li {
    text-align: left;
  }
}
@media (max-width: 781px) {
  .section-image-right .wp-block-spacer {
    height: 50px !important;
  }
}
@media (max-width: 781px) {
  .section-image-right .wp-block-spacer {
    height: 90px !important;
  }
}

@media (max-width: 781px) {
  .section-image-left .wp-block-heading {
    font-size: 32px !important;
    text-align: left;
    max-width: none;
  }
}
@media (max-width: 781px) {
  .section-image-left p {
    max-width: 100% !important;
  }
}
@media (max-width: 781px) {
  .section-image-left ul {
    max-width: 100% !important;
  }
}
@media (max-width: 781px) {
  .section-image-left ul li {
    text-align: left;
  }
}
@media (max-width: 781px) {
  .section-image-left .wp-block-spacer {
    height: 90px !important;
  }
}

@media (max-width: 781px) {
  .accordion-resources h3 {
    font-size: 36px !important;
  }
}

.waw-tabs__item {
  margin-top: 20px;
  border-bottom: unset !important;
}
@media (min-width: 1023px) {
  .waw-tabs__item {
    margin-top: 40px;
  }
}

@media (max-width: 1023px) {
  .media-columns-flex-overwrite .media-column-left {
    flex-basis: 100% !important;
  }
}
@media (min-width: 1024px) and (max-width: 1200px) {
  .media-columns-flex-overwrite .media-column-left {
    flex-basis: 60% !important;
  }
}
@media (max-width: 1023px) {
  .media-columns-flex-overwrite .media-column-right {
    flex-basis: 100% !important;
  }
}
@media (min-width: 1024px) and (max-width: 1200px) {
  .media-columns-flex-overwrite .media-column-right {
    flex-basis: 40% !important;
  }
}

/*
*  TABS ACCORDION
*/
@media (max-width: 1023px) {
  .wawContainer.container-block-tab .wawContainer__content {
    padding: 15px !important;
  }
}
.wawContainer.container-block-tab .waw-tabs__item-content {
  padding: 0 20px 20px 20px;
}
@media (max-width: 1023px) {
  .wawContainer.container-block-tab .waw-tabs__item-content {
    padding: 0 !important;
  }
}
.wawContainer.container-block-tab .da-accordion__item-content {
  padding: 0 20px 20px 20px;
}
@media (max-width: 1023px) {
  .wawContainer.container-block-tab .da-accordion__item-content {
    padding: 0 !important;
  }
}

/******************  BUTTONS  *******************/
/****************   NEW UPDATES  *****************/
.dm-list li {
  margin-bottom: 0 !important;
}

/******************  CONTAINER TWO TILES ***************************/
.container-post-two-tiles .wp-block-group {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.container-post-two-tiles figure {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

@media (max-width: 768px) {
  .container-post-two-tiles .wp-block-query.is-style-two-columns ul {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 1281px) {
  .container-post-two-tiles .is-content-justification-right,
  .container-post-two-tiles .is-layout-flex {
    justify-content: center;
  }
}
/****************************  banner-image-two-buttons **********************/
@media (max-width: 781px) {
  .waw-banner-image-two-buttons .is-content-justification-right,
  .waw-banner-image-two-buttons .is-layout-flex {
    justify-content: center;
  }
}
/* /**************************  PATTERN waw-icons-text-pattern ******************/
@media (max-width: 781px) {
  .waw-icons-text-pattern p {
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
  }
}
@media (max-width: 781px) {
  .hide-mobile {
    display: none;
  }
  .show-mobile {
    display: block;
  }
}
@media (min-width: 782px) {
  .hide-mobile {
    display: block;
  }
  .show-mobile {
    display: none;
  }
}
/******** PAGE EVENTS  ************/
@media (max-width: 1023px) {
  .wp-block-query.is-style-two-columns ul {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-auto-rows: minmax(min-content, max-content);
  }
}
.wp-block-button__link {
  min-width: 200px;
}

/*****  PAGE COURSES TWO COLUMNS QUERIES ****/
.key-information .col {
  padding-left: 0;
  padding-right: 0;
}

@media (max-width: 1023px) {
  .two-columns-size-overwrite .wp-block-columns.is-layout-flex.wp-container-core-columns-layout-10.wp-block-columns-is-layout-flex {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 781px) {
  .two-columns-size-overwrite .wp-block-query.is-style-two-columns ul {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-auto-rows: minmax(min-content, max-content);
    gap: 0 0;
  }
}
@media (min-width: 782px) and (max-width: 1023px) {
  .two-columns-size-overwrite .wp-block-query.is-style-two-columns ul {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: minmax(min-content, max-content);
    gap: 10px 20px;
  }
}
@media (max-width: 1280px) {
  .wp-block-table table {
    display: block;
  }
  .wp-block-table table td {
    white-space: nowrap;
    padding: 20px 5px !important;
  }
}
/****************  IMPORTANT DATES  ****************/
@media (max-width: 1023px) {
  .wawContainer.container-tabs-table .wawContainer__content {
    padding: 15px !important;
  }
}
@media (max-width: 1023px) {
  .wawContainer.container-tabs-table .waw-tabs__item-content {
    padding: 0;
  }
}
@media (max-width: 782px) {
  .wawContainer.container-tabs-table .wp-block-table.is-style-stripes tbody tr > td {
    border-right: unset;
    padding: 5px 20px;
  }
}
/**************************  POSTS CONTENT  **************/
.content-post .container {
  max-width: 75%;
}

@media (max-width: 781px) {
  .content-post .container {
    max-width: 90%;
  }
}
.hide-block-important {
  display: none;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-post-title {
  font-size: 28px;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .taxonomy-category {
  margin-bottom: 10px;
  font-size: 15px;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post figure.wp-block-post-featured-image img {
  border-radius: 6px;
  margin-bottom: 30px;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-post-excerpt {
  margin-bottom: 18px;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-read-more {
  display: flex;
  flex-direction: row;
  color: #d45e2b;
}

.query-loop-content-feature ul.wp-block-post-template li.wp-block-post .wp-block-read-more:after {
  position: relative;
  content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
  padding-left: 8px;
  top: 2px;
}

.wp-block-archives-list.wp-block-archives li {
  list-style: none;
}

.query-loop-archive ul.wp-block-post-template li.wp-block-post {
  margin-bottom: 30px;
}

.query-loop-archive ul.wp-block-post-template li.wp-block-post .wp-block-post-title {
  font-size: 15px;
}

/*************************  POST TILES 3 COLUMNS  ************************/
.query-loop--flat ul {
  gap: 15px;
}

@media (max-width: 781px) {
  .query-loop--flat ul.wp-container-core-post-template-layout-1 {
    grid-template-columns: 1fr;
    gap: 20px 20px;
  }
}
@media (min-width: 782px) and (max-width: 1023px) {
  .query-loop--flat ul.wp-container-core-post-template-layout-1 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: minmax(min-content, max-content);
    gap: 10px 20px;
  }
}
.query-loop--flat .wp-block-post .wp-block-post-featured-image img {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.query-loop--flat .wp-block-post .wp-block-group.has-background {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.query-loop--flat .wp-block-post .wawMeta {
  font-weight: bold;
}

.query-loop--flat .wp-block-post-excerpt__more-text {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 30px;
}

.query-loop--flat .wp-block-post-excerpt__more-text:after {
  position: relative;
  content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
  padding-left: 8px;
  top: 2px;
}

.query-loop--flat .wp-block-post-excerpt__more-text a {
  color: #d45e2b;
}

/*******************  LINK WITH ARROW  ****************/
@media (max-width: 781px) {
  .link-with-arrow.is-layout-flex {
    display: flex;
    flex-direction: row;
  }
}
/***********************  WHAT WE OFFER BLOCK  **************/
.what-we-offer-block h4 {
  margin-bottom: 5px;
}

/*********************  EVENTS PAGE  *********************/
@media (max-width: 781px) {
  .container-icons-events-page {
    width: 100%;
    padding: 10px 0;
  }
}
@media (max-width: 781px) {
  .container-icons-events-page .wp-block-group {
    width: 100%;
    padding-left: 0 !important;
    margin-bottom: 10px;
    flex-direction: row !important;
  }
}
/******************* TOP NAV BAR LOGIN SEARCH - NAVEGATION   *************/
.hello-bar__wrapper a:hover,
.wawNavigation a:hover {
  color: var(--wp--preset--color--primary) !important;
}

/********************* FLEX LEFT  *****************/
@media (max-width: 1023px) {
  .flex-left-on-mobile {
    flex-direction: row !important;
    padding: 0 15px !important;
  }
}
.element-90percent {
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}

/*********************  CATEGORIES TILES  ********************/
.wawCategories .wawCategories__wrapper {
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

@media (min-width: 1024px) and (max-width: 1200px) {
  .wawCategories .wawCategories__wrapper {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 872px) and (max-width: 1023px) {
  .wawCategories .wawCategories__wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 871px) {
  .wawCategories .wawCategories__wrapper {
    grid-template-columns: repeat(1, 1fr);
  }
}
.wawCategories .wawCategories__wrapper .wawCategories__item {
  margin-bottom: 20px;
}

.wawCategories .wawCategories__item-title svg {
  transition: all 0.4s ease-out;
}
.wawCategories .wawCategories__item-title:hover a {
  color: #d45e2b;
}
.wawCategories .wawCategories__item-title:hover svg {
  margin-right: 10px;
}

/********************* HEADER  *************/
@media (max-width: 871px) {
  .waw-header {
    margin-top: 0;
    margin-bottom: 0;
  }
}
@media (min-width: 872px) and (max-width: 1200px) {
  .waw-header {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
@media (max-width: 1023px) {
  .waw-header {
    flex-direction: row !important;
  }
}
.waw-header .wp-element-button.wp-block-button__link {
  padding: 0.667em 1.333em !important;
}

.dm-header-button .wp-block-button__link {
  min-width: 135px;
}

nav {
  transition: all 0.4s ease-out;
}
nav .wawNavigation nav ul {
  gap: 15px !important;
}
nav .wawNavigation nav ul li a {
  font-weight: 500;
}
nav .wawNavigation nav ul a {
  text-decoration: none;
}
nav .hello-bar__wrapper {
  padding-right: 10px;
}
@media (max-width: 500px) {
  nav .container-tabs-table .wp-block-table.is-style-stripes tbody tr > td {
    font-size: 10px;
    line-height: 14px;
  }
}
@media (max-width: 500px) {
  nav .container-tabs-table .wawContainer__content {
    padding: 0 !important;
  }
}
nav .container-tabs-table .wawContainer__content .container {
  padding: 0 !important;
}

/*******************************  IMPORTANT DATES  **************************/
@media (max-width: 640px) {
  .container-tabs-table .wp-block-table.is-style-stripes tbody tr > td {
    font-size: 10px;
    line-height: 14px;
  }
}
@media (max-width: 640px) {
  .container-tabs-table .wawContainer__content {
    padding: 0 !important;
  }
}
.container-tabs-table .wawContainer__content .container {
  padding: 0 !important;
}

.block-partner ul {
  margin-top: 25px;
}

.slider-logos .swiper-wrapper {
  justify-content: center !important;
}
.slider-logos .swiper-slide {
  width: auto !important;
}
.slider-logos .slider-row img {
  margin: 40px;
}

.slider-logos-2 .swiper-wrapper {
  justify-content: center !important;
}
.slider-logos-2 .swiper-slide {
  width: auto !important;
}
.slider-logos-2 .slider-row img {
  margin: 40px 80px;
}

.slider-logos-3 .swiper-wrapper {
  justify-content: center !important;
}
.slider-logos-3 .swiper-slide {
  width: auto !important;
}
.slider-logos-3 .slider-row img {
  margin: 40px;
}

@media (max-width: 768px) {
  .column-wrap {
    flex-direction: column;
  }
}

/**************** HOME PAGE CATEGORY  ****************/
.column-tiles-categories {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}
@media (max-width: 871px) {
  .column-tiles-categories {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (min-width: 872px) and (max-width: 1023px) {
  .column-tiles-categories {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) and (max-width: 1200px) {
  .column-tiles-categories {
    grid-template-columns: repeat(3, 1fr);
  }
}
.column-tiles-categories .column-tile {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.column-tiles-categories .column-tile figure {
  height: 246px;
}
.column-tiles-categories .column-tile figure img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
}
.column-tiles-categories .column-tile .column-tile-text {
  align-items: center;
  background-color: #fff;
  display: flex;
  height: 74px;
  justify-content: space-between;
  position: relative;
  flex-direction: row;
}
.column-tiles-categories .column-tile .column-tile-text:hover a {
  color: #d45e2b;
}
.column-tiles-categories .column-tile .column-tile-text:hover a:after {
  right: 30px;
}
.column-tiles-categories .column-tile .column-tile-text p {
  margin-bottom: 0;
}
.column-tiles-categories .column-tile .column-tile-text a {
  transition: all 0.4s ease-out;
}
.column-tiles-categories .column-tile .column-tile-text a:after {
  transition: all 0.4s ease-out;
  height: 20px;
  width: 12px;
  content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

/*
.category-group-tile{
	width: 25%;
    float: left;
    margin-left: -15px;
    padding-left: 15px;
    margin-right: 19px;
}
*/
.categories-container-tiles a {
  text-decoration: none;
}
@media (max-width: 871px) {
  .categories-container-tiles .col {
    grid-template-columns: repeat(1, 1fr) !important;
  }
}
@media (min-width: 872px) and (max-width: 1023px) {
  .categories-container-tiles .col {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}
@media (min-width: 1024px) and (max-width: 1200px) {
  .categories-container-tiles .col {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}
.categories-container-tiles .col figure {
  height: 246px;
}
.categories-container-tiles .col figure a,
.categories-container-tiles .col figure img {
  height: 100%;
}
.categories-container-tiles .col figure img {
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
}
.categories-container-tiles .col .column-tile-text {
  align-items: center;
  background-color: #fff;
  display: flex;
  height: 66px;
  justify-content: space-between;
  position: relative;
  flex-direction: row;
}
.categories-container-tiles .col .column-tile-text:hover a {
  color: #d45e2b;
}
.categories-container-tiles .col .column-tile-text:hover a:after {
  right: 30px;
}
.categories-container-tiles .col .column-tile-text p {
  margin-bottom: 0;
}
.categories-container-tiles .col .column-tile-text a {
  transition: all 0.4s ease-out;
}
.categories-container-tiles .col .column-tile-text a:after {
  transition: all 0.4s ease-out;
  height: 20px;
  width: 12px;
  content: url(/wp-content/uploads/2024/03/red-btn-arrow.svg);
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

/************************  TERMS AND CONDITIONS PAGE  ******************/
.terms-conditions-block ul li {
  margin-bottom: 5px;
  list-style: disc;
}
.terms-conditions-block ol li {
  margin-bottom: 5px;
}

/************************  HEADER   *******************/
.magnifying-glass {
  cursor: pointer;
}

/********************************  SEARCH  *********************************/
.search-results h2 {
  text-align: left !important;
}
@media (max-width: 871px) {
  .search-results h2 {
    font-size: 16px !important;
  }
}
.search-results .wp-block-query-pagination {
  margin-top: 40px;
}

/************************** HIDE HEADER SEARCH MENU ON SEARCH PAGE *******************/
.search-results .hide-on-search {
  display: none !important;
}

/**************************  NEW CAMPUS TILES SECTION ***************************/
@media (max-width: 1080px) {
  .columns-tiles-wrap {
    display: grid !important;
  }
}
.columns-tiles-wrap .column-tile {
  border-radius: 12px;
}
@media (min-width: 1080px) and (max-width: 1400px) {
  .columns-tiles-wrap .column-tile .wp-block-button__link {
    font-size: 14px;
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
.columns-tiles-wrap .column-tile figure img {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  min-width: 100%;
}
@media (max-width: 782px) {
  .columns-tiles-wrap .column-tile .columns-btn .wp-block-buttons {
    justify-content: center;
  }
}
@media (max-width: 782px) {
  .columns-tiles-wrap .column-tile .columns-btn .wp-block-buttons .wp-block-button {
    width: 80%;
  }
}

.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child {
  max-height: 350px;
  overflow: hidden;
  overflow-y: scroll;
  border-radius: 12px;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2509803922);
  /* width */
  /* Track */
  /* Handle */
  /* Handle on hover */
}
.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child::-webkit-scrollbar {
  width: 3px;
  margin-top: 5px;
  margin-bottom: 3px;
}
.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child::-webkit-scrollbar-track {
  background: #f1f1f1;
}
.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child::-webkit-scrollbar-thumb {
  background: #888;
}
.wp-swiper.is-style-overlayed-text-right .wp-swiper__slide-content .wp-block-columns .wp-block-column:last-child::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.wp-swiper__thumbs .wp-swiper__thumb {
  height: 175px;
  border-radius: 12px;
}
.wp-swiper__thumbs .wp-swiper__thumb img {
  border-radius: inherit;
}

/* Pre-loader CSS */
.page-loader {
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background: #fff;
  z-index: 1000;
}
.page-loader .txt {
  color: #666;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.3rem;
  font-weight: bold;
  line-height: 1.5;
}

/* Spinner animation */
.spinner {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  background-color: #ccc;
  border-radius: 100%;
  -webkit-animation: sk-scaleout 1s infinite ease-in-out;
  animation: sk-scaleout 1s infinite ease-in-out;
}

@-webkit-keyframes sk-scaleout {
  0% {
    -webkit-transform: scale(0);
    transform-origin: center center;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 0;
  }
}
@keyframes sk-scaleout {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    transform-origin: center center;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
}
:where(.wp-site-blocks) > * {
  margin-block-start: 0 !important;
  margin-block-end: 0 !important;
}

.wp-swiper.is-style-overlayed-text-right .wp_swiper__button-prev img,
.wp-swiper.is-style-overlayed-text-right .wp_swiper__button-next img {
  width: 45px;
  height: 45px;
}

.seperator-variant-1.wp-block-group.is-show-seperator > :not(:last-child):after {
  background-color: #fff !important;
}

.da-accordion__item:first-of-type {
  border-top: 1px solid #ddd;
}

.contaienr-height-400 .wawContainer__content .col {
  height: 600px;
}
